/**======================================================================
=========================================================================
Template Name: Dashboard Bootstrap 5 Admin Template
Author: Rajodiya Infotech
Support: [support link]
File: style.css
=========================================================================
=================================================================================== */
.pct-customizer {
    position: fixed;
    right: -360px;
    top: 160px;
    z-index: 1025;
    transition: all 0.15s ease-in-out;
}

.pct-customizer.active {
    right: 0;
}

.pct-customizer.active .pct-c-btn {
    padding-right: 0;
}

.pct-customizer .pct-c-btn {
    display: block;
    position: absolute;
    right: 100%;
    top: 0;
    transition: all 0.15s ease-in-out;
}

.pct-customizer .pct-c-btn .btn {
    padding: 17.5px 17.5px;
    display: block;
    border-radius: 8px 0 0 8px;
    box-shadow: 0 9px 9px -1px rgba(81, 69, 157, 0.3);
}

.pct-customizer .pct-c-btn .btn + .btn {
    margin-top: 8px;
}

.pct-customizer .pct-c-content {
    width: 360px;
    position: relative;
    top: 0;
    right: 0;
    background: #fff;
    overflow: hidden;
    border-radius: 0 0 0 4px;
    box-shadow: -9px 0 18px -1px rgba(69, 90, 100, 0.1);
}

.pct-customizer .pct-c-content .pct-header {
    padding: 20px 30px;
    border-bottom: 1px solid #f1f1f1;
}

.pct-customizer .pct-c-content .pct-body {
    padding: 20px 30px;
}

@media (max-width: 1024px) {
    .pct-customizer {
        display: none;
    }
}

.doc-img,
.theme-color {
    display: block;
    position: relative;
    padding: 0;
    margin-top: 10px;
    margin-bottom: 10px;
}

.doc-img > a,
  .theme-color > a {
    position: relative;
    width: 35px;
    height: 25px;
    border-radius: 3px;
    display: inline-block;
    background: #f8f9fd;
    overflow: hidden;
    box-shadow: 0 1px 2px rgba(0, 0, 0, 0.28);
}

.doc-img > a + a,
    .theme-color > a + a {
    margin-left: 5px;
}

.doc-img > a span,
    .theme-color > a span {
    width: 100%;
    position: absolute;
}

.doc-img > a span:after, .doc-img > a span:before,
      .theme-color > a span:after,
      .theme-color > a span:before {
    content: "";
    height: 100%;
    position: absolute;
}

.doc-img > a span:before,
      .theme-color > a span:before {
    width: 32%;
    left: 0;
    background: #1c232f;
}

.doc-img > a span:after,
      .theme-color > a span:after {
    width: 70%;
    right: 0;
    background: #f8f9fd;
}

.doc-img > a > span:nth-child(1),
    .theme-color > a > span:nth-child(1) {
    height: 40%;
    top: 0;
}

.doc-img > a > span:nth-child(1):after,
      .theme-color > a > span:nth-child(1):after {
    background: #fff;
}

.doc-img > a > span:nth-child(2),
    .theme-color > a > span:nth-child(2) {
    height: 66%;
    bottom: 0;
}

.doc-img.header-color > a[data-value="bg-primary"] > span:nth-child(1):after,
  .theme-color.header-color > a[data-value="bg-primary"] > span:nth-child(1):after {
    background: #51459d;
}

.doc-img.header-color > a[data-value="bg-success"] > span:nth-child(1):after,
  .theme-color.header-color > a[data-value="bg-success"] > span:nth-child(1):after {
    background: #6fd943;
}

.doc-img.header-color > a[data-value="bg-info"] > span:nth-child(1):after,
  .theme-color.header-color > a[data-value="bg-info"] > span:nth-child(1):after {
    background: #3ec9d6;
}

.doc-img.header-color > a[data-value="bg-warning"] > span:nth-child(1):after,
  .theme-color.header-color > a[data-value="bg-warning"] > span:nth-child(1):after {
    background: #ffa21d;
}

.doc-img.header-color > a[data-value="bg-danger"] > span:nth-child(1):after,
  .theme-color.header-color > a[data-value="bg-danger"] > span:nth-child(1):after {
    background: #ff3a6e;
}

.doc-img.header-color > a[data-value="bg-dark"] > span:nth-child(1):after,
  .theme-color.header-color > a[data-value="bg-dark"] > span:nth-child(1):after {
    background: #1c232f;
}

.doc-img.brand-color > a[data-value="bg-primary"] > span:nth-child(1):before,
  .theme-color.brand-color > a[data-value="bg-primary"] > span:nth-child(1):before {
    background: #51459d;
}

.doc-img.brand-color > a[data-value="bg-success"] > span:nth-child(1):before,
  .theme-color.brand-color > a[data-value="bg-success"] > span:nth-child(1):before {
    background: #6fd943;
}

.doc-img.brand-color > a[data-value="bg-info"] > span:nth-child(1):before,
  .theme-color.brand-color > a[data-value="bg-info"] > span:nth-child(1):before {
    background: #3ec9d6;
}

.doc-img.brand-color > a[data-value="bg-warning"] > span:nth-child(1):before,
  .theme-color.brand-color > a[data-value="bg-warning"] > span:nth-child(1):before {
    background: #ffa21d;
}

.doc-img.brand-color > a[data-value="bg-danger"] > span:nth-child(1):before,
  .theme-color.brand-color > a[data-value="bg-danger"] > span:nth-child(1):before {
    background: #ff3a6e;
}

.doc-img.brand-color > a[data-value="bg-dark"] > span:nth-child(1):before,
  .theme-color.brand-color > a[data-value="bg-dark"] > span:nth-child(1):before {
    background: #1c232f;
}

.doc-img.themes-color > a[data-value="theme-1"],
  .theme-color.themes-color > a[data-value="theme-1"] {
    background: linear-gradient(141.55deg, #0CAF60 3.46%, #0CAF60 99.86%), #0CAF60;
}

.doc-img.themes-color > a[data-value="theme-2"],
  .theme-color.themes-color > a[data-value="theme-2"] {
    background: linear-gradient(141.55deg, #6FD943 3.46%, #6FD943 99.86%), #6FD943;
}

.doc-img.themes-color > a[data-value="theme-3"],
  .theme-color.themes-color > a[data-value="theme-3"] {
    background: linear-gradient(141.55deg, #584ED2 3.46%, #584ED2 99.86%), #584ED2;
}

.doc-img.themes-color > a[data-value="theme-4"],
  .theme-color.themes-color > a[data-value="theme-4"] {
    background: linear-gradient(141.55deg, #145388 3.46%, #145388 99.86%), #145388;
}

.doc-img.themes-color > a[data-value="theme-5"],
  .theme-color.themes-color > a[data-value="theme-5"] {
    background: linear-gradient(141.55deg, #B9406B 3.46%, #B9406B 99.86%), #B9406B;
}

.doc-img.themes-color > a[data-value="theme-6"],
  .theme-color.themes-color > a[data-value="theme-6"] {
    background: linear-gradient(141.55deg, #008ECC 3.46%, #008ECC 99.86%), #008ECC;
}

.doc-img.themes-color > a[data-value="theme-7"],
  .theme-color.themes-color > a[data-value="theme-7"] {
    background: linear-gradient(141.55deg, #922C88 3.46%, #922C88 99.86%), #922C88;
}

.doc-img.themes-color > a[data-value="theme-8"],
  .theme-color.themes-color > a[data-value="theme-8"] {
    background: linear-gradient(141.55deg, #C0A145 3.46%, #C0A145 99.86%), #C0A145;
}

.doc-img.themes-color > a[data-value="theme-9"],
  .theme-color.themes-color > a[data-value="theme-9"] {
    background: linear-gradient(141.55deg, #48494B 3.46%, #48494B 99.86%), #48494B;
}

.doc-img.themes-color > a[data-value="theme-10"],
  .theme-color.themes-color > a[data-value="theme-10"] {
    background: linear-gradient(141.55deg, #0C7785 3.46%, #0C7785 99.86%), #0C7785;
}

.doc-img > a {
    width: 100px;
    height: 65px;
}

.dash-header[class*="bg-"], .dash-header.dark-header {
    box-shadow: none;
    background: #1c232f;
    color: #fff;
}

@media (min-width: 1025px) {
    .dash-header[class*="bg-"] .dash-head-link, .dash-header.dark-header .dash-head-link {
        color: #fff;
    }

    .dash-header[class*="bg-"] .dash-head-link i, .dash-header.dark-header .dash-head-link i {
        color: #fff;
    }

    .dash-header[class*="bg-"] .dash-head-link .material-icons-two-tone, .dash-header.dark-header .dash-head-link .material-icons-two-tone {
        background-color: #fff;
    }

    .dash-header[class*="bg-"] .dash-head-link.active, .dash-header[class*="bg-"] .dash-head-link:active, .dash-header[class*="bg-"] .dash-head-link:focus, .dash-header[class*="bg-"] .dash-head-link:hover, .dash-header.dark-header .dash-head-link.active, .dash-header.dark-header .dash-head-link:active, .dash-header.dark-header .dash-head-link:focus, .dash-header.dark-header .dash-head-link:hover {
        color: #fff;
        background: rgba(255, 255, 255, 0.15);
    }

    .dash-header[class*="bg-"] .dash-head-link.active .material-icons-two-tone, .dash-header[class*="bg-"] .dash-head-link:active .material-icons-two-tone, .dash-header[class*="bg-"] .dash-head-link:focus .material-icons-two-tone, .dash-header[class*="bg-"] .dash-head-link:hover .material-icons-two-tone, .dash-header.dark-header .dash-head-link.active .material-icons-two-tone, .dash-header.dark-header .dash-head-link:active .material-icons-two-tone, .dash-header.dark-header .dash-head-link:focus .material-icons-two-tone, .dash-header.dark-header .dash-head-link:hover .material-icons-two-tone {
        background-color: #fff;
    }

    .dash-header[class*="bg-"] .dash-head-link .user-desc, .dash-header.dark-header .dash-head-link .user-desc {
        color: rgba(255, 255, 255, 0.7);
    }
}

.dash-sidebar.light-sidebar {
    background: #fff;
    box-shadow: 0 1px 20px 0 rgba(69, 90, 100, 0.08);
}

.dash-sidebar.light-sidebar .dash-caption {
    color: #333333;
}

.dash-sidebar.light-sidebar .dash-navbar > .dash-item > .dash-link {
    border-radius: 12px;
    margin-left: 15px;
    margin-right: 15px;
    padding: 7px 10px 7px 7px;
}

.dash-sidebar.light-sidebar .dash-link {
    color: #333333;
    font-size: 14px;
}

.dash-sidebar.light-sidebar .dash-link .dash-micon {
    background-color: #fff;
    box-shadow: -3px 4px 23px rgba(0, 0, 0, 0.1);
}

.dash-sidebar.light-sidebar .dash-link .dash-micon i,
      .dash-sidebar.light-sidebar .dash-link .dash-micon svg {
    color: #525b69;
    stroke: #525b69;
    fill: #f2f2f2;
}

.dash-sidebar.light-sidebar .dash-link .dash-micon .material-icons-two-tone {
    background-color: #525b69;
}

.dash-sidebar.light-sidebar .dash-link .dash-arrow {
    margin-top: 7px;
}

.dash-sidebar.light-sidebar .dash-link:active, .dash-sidebar.light-sidebar .dash-link:focus, .dash-sidebar.light-sidebar .dash-link:hover {
    color: #51459d;
}

.dash-sidebar.light-sidebar .dash-submenu .dash-link .dash-arrow {
    margin-top: 2px;
}

.dash-sidebar.light-sidebar .dash-item.active > .dash-link,
  .dash-sidebar.light-sidebar .dash-item:hover > .dash-link {
    color: #51459d;
}

.dash-sidebar.light-sidebar .dash-item.active > .dash-link i,
    .dash-sidebar.light-sidebar .dash-item:hover > .dash-link i {
    color: #51459d;
}

.dash-sidebar.light-sidebar .dash-item.active > .dash-link .dash-micon i.material-icons-two-tone,
    .dash-sidebar.light-sidebar .dash-item:hover > .dash-link .dash-micon i.material-icons-two-tone {
    background-color: #51459d;
}

.dash-sidebar.light-sidebar .dash-navbar > .dash-item.active > .dash-link {
    font-weight: 700;
}

.dash-sidebar.light-sidebar .dash-navbar > .dash-item.active > .dash-link, .dash-sidebar.light-sidebar .dash-navbar > .dash-item:focus > .dash-link, .dash-sidebar.light-sidebar .dash-navbar > .dash-item:hover > .dash-link {
    box-shadow: -6px 11px 19px rgba(0, 0, 0, 0.04);
    background: #fff;
    color: #333333;
}

.minimenu .dash-sidebar.light-sidebar .dash-navbar > .dash-item > .dash-link {
    padding: 20px 25px;
    margin-left: 0;
    margin-right: 0;
    border-radius: 0;
}

.minimenu .dash-sidebar.light-sidebar .dash-item .dash-item:hover > .dash-link {
    background: transparent;
}

.minimenu .dash-sidebar.light-sidebar .dash-submenu {
    box-shadow: 0 1px 20px 0 rgba(69, 90, 100, 0.08);
    background: #fff;
}

.dash-horizontal .topbar.light-sidebar {
    background: #fff;
    box-shadow: 0 1px 20px 0 rgba(69, 90, 100, 0.08);
}

.dash-horizontal .topbar.light-sidebar .dash-link {
    color: #333333;
}

.dash-horizontal .topbar.light-sidebar .dash-link:active, .dash-horizontal .topbar.light-sidebar .dash-link:focus, .dash-horizontal .topbar.light-sidebar .dash-link:hover {
    color: #51459d;
}

.auth-wrapper ~ .pct-customizer {
    display: none;
}

/*# sourceMappingURL=data:application/json;charset=utf8;base64,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 */
