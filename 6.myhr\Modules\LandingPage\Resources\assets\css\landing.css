/**======================================================================
=========================================================================
Template Name: Dashboard Bootstrap 5 Admin Template
Author: Rajodiya Infotech
Support: [support link]
File: style.css
=========================================================================
=================================================================================== */
body {
    margin: 0;
    overflow-x: hidden;
    background: white;
}

a {
    outline: none;
    text-decoration: none;
}

a:focus, a:hover {
    outline: none;
    text-decoration: none;
}

section {
    padding-top: 90px;
    padding-bottom: 90px;
}

.bg-dark {
    background: #1c232f !important;
    color: #fff;
}

.bg-dark p {
    color: #b5bdca;
}

.bg-dark .title h2 {
    color: rgba(255, 255, 255, 0.7);
}

.bg-dark .title h2 span {
    color: #fff;
}

.theme-alt-bg {
    background: #ededed;
}

.title {
    position: relative;
    text-align: center;
    margin-bottom: 50px;
}

.title h2 {
    margin-bottom: 15px;
    z-index: 3;
    color: #293240;
    margin-top: 15px;
    text-transform: capitalize;
}

.title h2 span {
    font-weight: 600;
    color: #060606;
}

.title .material-icons-two-tone {
    font-size: 40px;
}

@media only screen and (max-width: 992px) {
    .title {
        margin-bottom: 30px;
    }

    .title h2 {
        margin-bottom: 10px;
    }
}

.navbar {
    position: fixed;
    z-index: 1000;
    width: 100%;
    height: 70px;
    top: -80px;
    -webkit-backdrop-filter: blur(5px);
    backdrop-filter: blur(5px);
    transition: all 0.3s linear;
    margin-bottom: 0;
    box-shadow: 0 8px 6px -10px rgba(0, 0, 0, 0.5);
}

.navbar.default {
    top: 0;
    background: rgba(28, 35, 47, 0.9);
}

.navbar.default.top-nav-collapse {
    background: transparent;
    box-shadow: none;
}

@media (min-width: 767px) {
    .navbar .navbar-nav .nav-link {
        position: relative;
    }

    .navbar .navbar-nav .nav-link.active:after {
        left: 15%;
        right: 15%;
    }
}

header:not(.price-header) {
    position: relative;
    color: #fff;
    min-height: 60vh;
    padding-top: 160px;
    padding-bottom: 90px;
    display: flex;
    align-items: center;
}

header:not(.price-header) > * {
    position: relative;
    z-index: 5;
}

header:not(.price-header):after, header:not(.price-header):before {
    content: "";
    position: absolute;
    top: -100px;
    left: 0;
    right: 0;
    bottom: 0;
}

header:not(.price-header):before {
    background-attachment: fixed;
    z-index: 1;
}

header.price-header {
    position: relative;
    min-height: 60vh;
    padding-top: 160px;
    padding-bottom: 90px;
    display: flex;
    align-items: center;
}

.dashboard-block {
    position: relative;
    overflow: hidden;
}

.dashboard-block .img-dashboard {
    margin-bottom: -15%;
}

.feature {
    text-align: center;
}

.feature .card-body {
    padding-top: 40px;
    padding-bottom: 40px;
}

.feature .theme-avtar {
    width: 130px;
    height: 130px;
    font-size: 45px;
    margin: 0 auto;
    border-radius: 50px;
}

.price-section {
    position: relative;
}

.price-section > * {
    position: relative;
    z-index: 5;
}

.price-section::after {
    content: "";
    position: absolute;
    left: 0;
    right: 0;
    bottom: 0;
    top: 50%;
    z-index: 1;
    background: linear-gradient(180deg, #cecece 0%, #ffffff 100%);
}

.faq .accordion .accordion-item {
    border-radius: 10px;
    overflow: hidden;
    border: none;
    margin-bottom: 10px;
}

.faq .accordion .accordion-item .accordion-button {
    font-weight: 700;
    padding: 1.3rem 1.25rem;
}

.faq .accordion .accordion-item .accordion-button span > i {
    font-size: 20px;
    margin-right: 8px;
}

.faq .accordion .accordion-item .accordion-button:not(.collapsed) {
    border-radius: 10px;
    background: transparent;
    box-shadow: 0 6px 30px rgba(182, 186, 203, 0.3);
}

.faq .accordion .accordion-item .accordion-body {
    padding: 2.3rem 2.3rem 2.3rem 3rem;
}

.price-card {
    text-align: center;
    position: relative;
    margin-top: 30px;
}

.price-card.price-2 {
    color: #fff;
}

.price-card.price-2 .price-badge {
    color: #fff;
    background: #1c232f;
}

.price-card .p-price {
    font-size: 80px;
}

.price-card .price-badge {
    color: #fff;
    padding: 7px 24px;
    border-radius: 30px;
    position: absolute;
    top: 0;
    left: 50%;
    transform: translate(-50%, -50%);
}

.price-card .list-unstyled {
    display: inline-block;
}

.price-card .list-unstyled li {
    display: flex;
    align-items: center;
}

.price-card .list-unstyled li + li {
    margin-top: 8px;
}

.price-card .list-unstyled .theme-avtar {
    display: inline-flex;
    width: 30px;
    height: 30px;
    border-radius: 10px;
    background: #fff;
    margin-right: 15px;
}

.side-feature {
    overflow: hidden;
}

.side-feature .feature-img-row {
    width: 80vw;
}

@media only screen and (max-width: 992px) {
    header,
  section {
        padding-top: 40px;
        padding-bottom: 40px;
    }
}

@media only screen and (max-width: 768px) {
    .navbar {
        height: auto;
    }
}

/*# sourceMappingURL=data:application/json;charset=utf8;base64,eyJ2ZXJzaW9uIjozLCJzb3VyY2VzIjpbImxhbmRpbmcuc2NzcyIsImxhbmRpbmcuY3NzIiwic2V0dGluZ3MvX2N1c3RvbS12YXJpYWJsZXMuc2NzcyIsInNldHRpbmdzL190aGVtZS12YXJpYWJsZXMuc2NzcyJdLCJuYW1lcyI6W10sIm1hcHBpbmdzIjoiQUFBQTs7Ozs7OztxRkNPcUY7QURPckY7RUFDRSxTQUFTO0VBQ1Qsa0JBQWtCO0VBQ2xCLGlCQUFpQyxFQUFBOztBQUduQztFQUNFLGFBQWE7RUFDYixxQkFBcUIsRUFBQTtFQUZ2QjtJQU1JLGFBQWE7SUFDYixxQkFBcUIsRUFBQTs7QUFJekI7RUFDRSxpQkFBaUI7RUFDakIsb0JBQW9CLEVBQUE7O0FBR3RCO0VBQ0UsOEJBQTRCO0VBQzVCLFdBQVcsRUFBQTtFQUZiO0lBS0ksY0FBYyxFQUFBO0VBTGxCO0lBVU0sK0JBQWdDLEVBQUE7SUFWdEM7TUFhUSxXQUFXLEVBQUE7O0FBTW5CO0VBQ0UsbUJBQW1CLEVBQUE7O0FBR3JCO0VBQ0Usa0JBQWtCO0VBQ2xCLGtCQUFrQjtFQUNsQixtQkFBbUIsRUFBQTtFQUhyQjtJQU1JLG1CQUFtQjtJQUNuQixVQUFVO0lBQ1YsY0VrVWdCO0lGalVoQixnQkFBZ0I7SUFDaEIsMEJBQTBCLEVBQUE7SUFWOUI7TUFhTSxnQkFBZ0I7TUFDaEIsY0V3Z0JrQixFQUFBO0VGdGhCeEI7SUFtQkksZUFBZSxFQUFBO0VBRWpCO0lBckJGO01Bc0JJLG1CQUFtQixFQUFBO01BdEJ2QjtRQXlCTSxtQkFBbUIsRUFBQSxFQUNwQjs7QUFLTDtFQUNFLGVBQWU7RUFDZixhQUFhO0VBQ2IsV0FBVztFQUNYLFlBQVk7RUFDWixVQUFVO0VBQ1Ysa0NBQTBCO1VBQTFCLDBCQUEwQjtFQUUxQiwyQkFBMkI7RUFDM0IsZ0JBQWdCO0VBQ2hCLDhDQUE4QyxFQUFBO0VBVmhEO0lBYUksTUFBTTtJQUNOLGlDQUF1QyxFQUFBO0VBZDNDO0lBaUJJLHVCQUF1QjtJQUN2QixnQkFBZ0IsRUFBQTtFQUVsQjtJQXBCRjtNQXVCUSxrQkFBa0IsRUFBQTtNQXZCMUI7UUFxQ1ksU0FBUztRQUNULFVBQVUsRUFBQSxFQUNYOztBQVNYO0VBQ0Usa0JBQWtCO0VBQ2xCLFdBQVc7RUFDWCxnQkFBZ0I7RUFDaEIsa0JBQWtCO0VBQ2xCLG9CQUFvQjtFQUNwQixhQUFhO0VBQ2IsbUJBQW1CLEVBQUE7RUFQckI7SUFVSSxrQkFBa0I7SUFDbEIsVUFBVSxFQUFBO0VBWGQ7SUFlSSxXQUFXO0lBQ1gsa0JBQWtCO0lBQ2xCLFdBQVc7SUFDWCxPQUFPO0lBQ1AsUUFBUTtJQUNSLFNBQVMsRUFBQTtFQXBCYjtJQXdCSSw0QkFBNEI7SUFDNUIsVUFBVSxFQUFBOztBQVFkO0VBQ0Usa0JBQWtCO0VBQ2xCLGdCQUFnQjtFQUNoQixrQkFBa0I7RUFDbEIsb0JBQW9CO0VBQ3BCLGFBQWE7RUFDYixtQkFBbUIsRUFBQTs7QUFHckI7RUFDRSxrQkFBa0I7RUFDbEIsZ0JBQWdCLEVBQUE7RUFGbEI7SUFJSSxtQkFBbUIsRUFBQTs7QUFHdkI7RUFDRSxrQkFBa0IsRUFBQTtFQURwQjtJQUdJLGlCQUFpQjtJQUNqQixvQkFBb0IsRUFBQTtFQUp4QjtJQU9JLFlBQVk7SUFDWixhQUFhO0lBQ2IsZUFBZTtJQUNmLGNBQWM7SUFDZCxtQkFBbUIsRUFBQTs7QUFHdkI7RUFDRSxrQkFBa0IsRUFBQTtFQURwQjtJQUdJLGtCQUFrQjtJQUNsQixVQUFVLEVBQUE7RUFKZDtJQU9JLFdBQVc7SUFDWCxrQkFBa0I7SUFDbEIsT0FBTztJQUNQLFFBQVE7SUFDUixTQUFTO0lBQ1QsUUFBUTtJQUNSLFVBQVU7SUFDViw2REFBNkQsRUFBQTs7QUFHakU7RUFHTSxtQkU0UmM7RUYzUmQsZ0JBQWdCO0VBQ2hCLFlBQVk7RUFDWixtQkFBbUIsRUFBQTtFQU56QjtJQVFRLGdCQUFnQjtJQUNoQix1QkFBdUIsRUFBQTtJQVQvQjtNQVdVLGVBQWU7TUFDZixpQkFBaUIsRUFBQTtJQVozQjtNQWVVLG1CRWdSVTtNRi9RVix1QkFBdUI7TUFDdkIsK0NHMU11QyxFQUFBO0VIeUxqRDtJQXFCUSxrQ0FBa0MsRUFBQTs7QUFLMUM7RUFDRSxrQkFBa0I7RUFDbEIsa0JBQWtCO0VBQ2xCLGdCQUFnQixFQUFBO0VBSGxCO0lBZUksV0FBVyxFQUFBO0lBZmY7TUFpQk0sV0FBVztNQUNYLG1CRWpNUSxFQUFBO0VGK0tkO0lBeUJJLGVBQWUsRUFBQTtFQXpCbkI7SUE0QkksV0FBVztJQUNYLGlCQUFpQjtJQUNqQixtQkFBbUI7SUFDbkIsa0JBQWtCO0lBQ2xCLE1BQU07SUFDTixTQUFTO0lBQ1QsZ0NBQWdDLEVBQUE7RUFsQ3BDO0lBcUNJLHFCQUFxQixFQUFBO0lBckN6QjtNQXVDTSxhQUFhO01BQ2IsbUJBQW1CLEVBQUE7TUF4Q3pCO1FBMENRLGVBQWUsRUFBQTtJQTFDdkI7TUE4Q00sb0JBQW9CO01BQ3BCLFdBQVc7TUFDWCxZQUFZO01BQ1osbUJBQW1CO01BQ25CLGdCQUFnQjtNQUNoQixrQkFBa0IsRUFBQTs7QUFJeEI7RUFDRSxnQkFBZ0IsRUFBQTtFQURsQjtJQUdJLFdBQVcsRUFBQTs7QUFHZjtFQUNFOztJQUVFLGlCQUFpQjtJQUNqQixvQkFBb0IsRUFBQSxFQUNyQjs7QUFFSDtFQUNFO0lBQ0UsWUFBWSxFQUFBLEVBQ2IiLCJmaWxlIjoibGFuZGluZy5jc3MiLCJzb3VyY2VzQ29udGVudCI6WyIvKio9PT09PT09PT09PT09PT09PT09PT09PT09PT09PT09PT09PT09PT09PT09PT09PT09PT09PT09PT09PT09PT09PT09PT09XG49PT09PT09PT09PT09PT09PT09PT09PT09PT09PT09PT09PT09PT09PT09PT09PT09PT09PT09PT09PT09PT09PT09PT09PT09XG5UZW1wbGF0ZSBOYW1lOiBEYXNoYm9hcmQgQm9vdHN0cmFwIDUgQWRtaW4gVGVtcGxhdGVcbkF1dGhvcjogUmFqb2RpeWEgSW5mb3RlY2hcblN1cHBvcnQ6IFtzdXBwb3J0IGxpbmtdXG5GaWxlOiBzdHlsZS5jc3Ncbj09PT09PT09PT09PT09PT09PT09PT09PT09PT09PT09PT09PT09PT09PT09PT09PT09PT09PT09PT09PT09PT09PT09PT09PT1cbj09PT09PT09PT09PT09PT09PT09PT09PT09PT09PT09PT09PT09PT09PT09PT09PT09PT09PT09PT09PT09PT09PT09PT09PT09PT09PT09PT09ICovXG4vLyBtYWluIGZyYW1ld29ya1xuQGltcG9ydCBcIm5vZGVfbW9kdWxlcy9ib290c3RyYXAvc2Nzcy9mdW5jdGlvbnNcIjtcbkBpbXBvcnQgXCJub2RlX21vZHVsZXMvYm9vdHN0cmFwL3Njc3MvdmFyaWFibGVzXCI7XG5AaW1wb3J0IFwic2V0dGluZ3MvY3VzdG9tLXZhcmlhYmxlc1wiO1xuQGltcG9ydCBcInNldHRpbmdzL3RoZW1lLXZhcmlhYmxlc1wiO1xuXG5ib2R5IHtcbiAgbWFyZ2luOiAwO1xuICBvdmVyZmxvdy14OiBoaWRkZW47XG4gIGJhY2tncm91bmQ6IGxpZ2h0ZW4oJGJvZHktYmcsIDUlKTtcbn1cblxuYSB7XG4gIG91dGxpbmU6IG5vbmU7XG4gIHRleHQtZGVjb3JhdGlvbjogbm9uZTtcblxuICAmOmZvY3VzLFxuICAmOmhvdmVyIHtcbiAgICBvdXRsaW5lOiBub25lO1xuICAgIHRleHQtZGVjb3JhdGlvbjogbm9uZTtcbiAgfVxufVxuXG5zZWN0aW9uIHtcbiAgcGFkZGluZy10b3A6IDkwcHg7XG4gIHBhZGRpbmctYm90dG9tOiA5MHB4O1xufVxuXG4uYmctZGFyayB7XG4gIGJhY2tncm91bmQ6ICRkYXJrICFpbXBvcnRhbnQ7XG4gIGNvbG9yOiAjZmZmO1xuXG4gIHAge1xuICAgIGNvbG9yOiAjYjViZGNhO1xuICB9XG5cbiAgLnRpdGxlIHtcbiAgICBoMiB7XG4gICAgICBjb2xvcjogdHJhbnNwYXJlbnRpemUoI2ZmZiwgMC4zKTtcblxuICAgICAgc3BhbiB7XG4gICAgICAgIGNvbG9yOiAjZmZmO1xuICAgICAgfVxuICAgIH1cbiAgfVxufVxuXG4udGhlbWUtYWx0LWJnIHtcbiAgYmFja2dyb3VuZDogI2VkZWRlZDtcbn1cblxuLnRpdGxlIHtcbiAgcG9zaXRpb246IHJlbGF0aXZlO1xuICB0ZXh0LWFsaWduOiBjZW50ZXI7XG4gIG1hcmdpbi1ib3R0b206IDUwcHg7XG5cbiAgaDIge1xuICAgIG1hcmdpbi1ib3R0b206IDE1cHg7XG4gICAgei1pbmRleDogMztcbiAgICBjb2xvcjogJGJvZHktY29sb3I7XG4gICAgbWFyZ2luLXRvcDogMTVweDtcbiAgICB0ZXh0LXRyYW5zZm9ybTogY2FwaXRhbGl6ZTtcblxuICAgIHNwYW4ge1xuICAgICAgZm9udC13ZWlnaHQ6IDYwMDtcbiAgICAgIGNvbG9yOiAkaGVhZGluZ3MtY29sb3I7XG4gICAgfVxuICB9XG5cbiAgLm1hdGVyaWFsLWljb25zLXR3by10b25lIHtcbiAgICBmb250LXNpemU6IDQwcHg7XG4gIH1cbiAgQG1lZGlhIG9ubHkgc2NyZWVuIGFuZCAobWF4LXdpZHRoOiA5OTJweCkge1xuICAgIG1hcmdpbi1ib3R0b206IDMwcHg7XG5cbiAgICBoMiB7XG4gICAgICBtYXJnaW4tYm90dG9tOiAxMHB4O1xuICAgIH1cbiAgfVxufVxuLy8gWyBuYXZiYXIgXSBTdGFydFxuXG4ubmF2YmFyIHtcbiAgcG9zaXRpb246IGZpeGVkO1xuICB6LWluZGV4OiAxMDAwO1xuICB3aWR0aDogMTAwJTtcbiAgaGVpZ2h0OiA3MHB4O1xuICB0b3A6IC04MHB4O1xuICBiYWNrZHJvcC1maWx0ZXI6IGJsdXIoNXB4KTtcbiAgLy8gYmFja2dyb3VuZDogcmdiYSgyMiwgMjgsIDM3LCAwLjkpO1xuICB0cmFuc2l0aW9uOiBhbGwgMC4zcyBsaW5lYXI7XG4gIG1hcmdpbi1ib3R0b206IDA7XG4gIGJveC1zaGFkb3c6IDAgOHB4IDZweCAtMTBweCByZ2JhKDAsIDAsIDAsIDAuNSk7XG5cbiAgJi5kZWZhdWx0IHtcbiAgICB0b3A6IDA7XG4gICAgYmFja2dyb3VuZDogdHJhbnNwYXJlbnRpemUoJGRhcmssIDAuMSUpO1xuICB9XG4gICYuZGVmYXVsdC50b3AtbmF2LWNvbGxhcHNlIHtcbiAgICBiYWNrZ3JvdW5kOiB0cmFuc3BhcmVudDtcbiAgICBib3gtc2hhZG93OiBub25lO1xuICB9XG4gIEBtZWRpYSAobWluLXdpZHRoOiA3NjdweCkge1xuICAgIC5uYXZiYXItbmF2IHtcbiAgICAgIC5uYXYtbGluayB7XG4gICAgICAgIHBvc2l0aW9uOiByZWxhdGl2ZTtcblxuICAgICAgICAvLyAmOmFmdGVyIHtcbiAgICAgICAgLy8gICBjb250ZW50OiBcIlwiO1xuICAgICAgICAvLyAgIHBvc2l0aW9uOiBhYnNvbHV0ZTtcbiAgICAgICAgLy8gICBoZWlnaHQ6IDNweDtcbiAgICAgICAgLy8gICBib3R0b206IDA7XG4gICAgICAgIC8vICAgYmFja2dyb3VuZDogJHByaW1hcnk7XG4gICAgICAgIC8vICAgbGVmdDogNTAlO1xuICAgICAgICAvLyAgIHJpZ2h0OiA1MCU7XG4gICAgICAgIC8vIH1cblxuICAgICAgICAmLmFjdGl2ZSB7XG4gICAgICAgICAgJjphZnRlciB7XG4gICAgICAgICAgICBsZWZ0OiAxNSU7XG4gICAgICAgICAgICByaWdodDogMTUlO1xuICAgICAgICAgIH1cbiAgICAgICAgfVxuICAgICAgfVxuICAgIH1cbiAgfVxufVxuLy8gWyBuYXbhuYdiYXIgXSBlbmRcbi8vIFsgaGVhZGVyIF0gU3RhcnRcblxuaGVhZGVyOm5vdCgucHJpY2UtaGVhZGVyKSB7XG4gIHBvc2l0aW9uOiByZWxhdGl2ZTtcbiAgY29sb3I6ICNmZmY7XG4gIG1pbi1oZWlnaHQ6IDYwdmg7XG4gIHBhZGRpbmctdG9wOiAxNjBweDtcbiAgcGFkZGluZy1ib3R0b206IDkwcHg7XG4gIGRpc3BsYXk6IGZsZXg7XG4gIGFsaWduLWl0ZW1zOiBjZW50ZXI7XG5cbiAgPiAqIHtcbiAgICBwb3NpdGlvbjogcmVsYXRpdmU7XG4gICAgei1pbmRleDogNTtcbiAgfVxuICAmOmFmdGVyLFxuICAmOmJlZm9yZSB7XG4gICAgY29udGVudDogXCJcIjtcbiAgICBwb3NpdGlvbjogYWJzb2x1dGU7XG4gICAgdG9wOiAtMTAwcHg7XG4gICAgbGVmdDogMDtcbiAgICByaWdodDogMDtcbiAgICBib3R0b206IDA7XG4gIH1cblxuICAmOmJlZm9yZSB7XG4gICAgYmFja2dyb3VuZC1hdHRhY2htZW50OiBmaXhlZDtcbiAgICB6LWluZGV4OiAxO1xuICB9XG5cbiAgLy8gJjphZnRlciB7XG4gIC8vIGJhY2tncm91bmQtY29sb3I6IHRyYW5zcGFyZW50aXplKCRwcmltYXJ5LCAwKTtcbiAgLy8gei1pbmRleDogMjtcbiAgLy8gfVxufVxuaGVhZGVyLnByaWNlLWhlYWRlciB7XG4gIHBvc2l0aW9uOiByZWxhdGl2ZTtcbiAgbWluLWhlaWdodDogNjB2aDtcbiAgcGFkZGluZy10b3A6IDE2MHB4O1xuICBwYWRkaW5nLWJvdHRvbTogOTBweDtcbiAgZGlzcGxheTogZmxleDtcbiAgYWxpZ24taXRlbXM6IGNlbnRlcjtcbn1cbi8vIFsgaGVhZGVyIF0gZW5kXG4uZGFzaGJvYXJkLWJsb2NrIHtcbiAgcG9zaXRpb246IHJlbGF0aXZlO1xuICBvdmVyZmxvdzogaGlkZGVuO1xuICAuaW1nLWRhc2hib2FyZCB7XG4gICAgbWFyZ2luLWJvdHRvbTogLTE1JTtcbiAgfVxufVxuLmZlYXR1cmUge1xuICB0ZXh0LWFsaWduOiBjZW50ZXI7XG4gIC5jYXJkLWJvZHkge1xuICAgIHBhZGRpbmctdG9wOiA0MHB4O1xuICAgIHBhZGRpbmctYm90dG9tOiA0MHB4O1xuICB9XG4gIC50aGVtZS1hdnRhciB7XG4gICAgd2lkdGg6IDEzMHB4O1xuICAgIGhlaWdodDogMTMwcHg7XG4gICAgZm9udC1zaXplOiA0NXB4O1xuICAgIG1hcmdpbjogMCBhdXRvO1xuICAgIGJvcmRlci1yYWRpdXM6IDUwcHg7XG4gIH1cbn1cbi5wcmljZS1zZWN0aW9uIHtcbiAgcG9zaXRpb246IHJlbGF0aXZlO1xuICA+ICoge1xuICAgIHBvc2l0aW9uOiByZWxhdGl2ZTtcbiAgICB6LWluZGV4OiA1O1xuICB9XG4gICY6OmFmdGVyIHtcbiAgICBjb250ZW50OiBcIlwiO1xuICAgIHBvc2l0aW9uOiBhYnNvbHV0ZTtcbiAgICBsZWZ0OiAwO1xuICAgIHJpZ2h0OiAwO1xuICAgIGJvdHRvbTogMDtcbiAgICB0b3A6IDUwJTtcbiAgICB6LWluZGV4OiAxO1xuICAgIGJhY2tncm91bmQ6IGxpbmVhci1ncmFkaWVudCgxODBkZWcsICNjZWNlY2UgMCUsICNmZmZmZmYgMTAwJSk7XG4gIH1cbn1cbi5mYXEge1xuICAuYWNjb3JkaW9uIHtcbiAgICAuYWNjb3JkaW9uLWl0ZW0ge1xuICAgICAgYm9yZGVyLXJhZGl1czogJGJvcmRlci1yYWRpdXM7XG4gICAgICBvdmVyZmxvdzogaGlkZGVuO1xuICAgICAgYm9yZGVyOiBub25lO1xuICAgICAgbWFyZ2luLWJvdHRvbTogMTBweDtcbiAgICAgIC5hY2NvcmRpb24tYnV0dG9uIHtcbiAgICAgICAgZm9udC13ZWlnaHQ6IDcwMDtcbiAgICAgICAgcGFkZGluZzogMS4zcmVtIDEuMjVyZW07XG4gICAgICAgIHNwYW4gPiBpIHtcbiAgICAgICAgICBmb250LXNpemU6IDIwcHg7XG4gICAgICAgICAgbWFyZ2luLXJpZ2h0OiA4cHg7XG4gICAgICAgIH1cbiAgICAgICAgJjpub3QoLmNvbGxhcHNlZCkge1xuICAgICAgICAgIGJvcmRlci1yYWRpdXM6ICRib3JkZXItcmFkaXVzO1xuICAgICAgICAgIGJhY2tncm91bmQ6IHRyYW5zcGFyZW50O1xuICAgICAgICAgIGJveC1zaGFkb3c6ICRjYXJkLXNoYWRvdztcbiAgICAgICAgfVxuICAgICAgfVxuICAgICAgLmFjY29yZGlvbi1ib2R5IHtcbiAgICAgICAgcGFkZGluZzogMi4zcmVtIDIuM3JlbSAyLjNyZW0gM3JlbTtcbiAgICAgIH1cbiAgICB9XG4gIH1cbn1cbi5wcmljZS1jYXJkIHtcbiAgdGV4dC1hbGlnbjogY2VudGVyO1xuICBwb3NpdGlvbjogcmVsYXRpdmU7XG4gIG1hcmdpbi10b3A6IDMwcHg7XG4gICYucHJpY2UtMSB7XG4gICAgLy8gYmFja2dyb3VuZDogIzgwODE5MTtcbiAgICAvLyBjb2xvcjogI2ZmZjtcbiAgfVxuICAmLnByaWNlLTIge1xuICAgIC8vIGJhY2tncm91bmQ6IGxpbmVhci1ncmFkaWVudChcbiAgICAvLyAgICAgMTQxLjU1ZGVnLFxuICAgIC8vICAgICByZ2JhKDgxLCA2OSwgMTU3LCAwKSAzLjQ2JSxcbiAgICAvLyAgICAgcmdiYSgyNTUsIDU4LCAxMTAsIDAuNikgOTkuODYlXG4gICAgLy8gICApLFxuICAgIC8vICAgIzUxNDU5ZDtcbiAgICBjb2xvcjogI2ZmZjtcbiAgICAucHJpY2UtYmFkZ2Uge1xuICAgICAgY29sb3I6ICNmZmY7XG4gICAgICBiYWNrZ3JvdW5kOiAkZGFyaztcbiAgICB9XG4gIH1cbiAgJi5wcmljZS0zIHtcbiAgICAvLyBiYWNrZ3JvdW5kOiAjY2VjZWNlO1xuICB9XG4gIC5wLXByaWNlIHtcbiAgICBmb250LXNpemU6IDgwcHg7XG4gIH1cbiAgLnByaWNlLWJhZGdlIHtcbiAgICBjb2xvcjogI2ZmZjtcbiAgICBwYWRkaW5nOiA3cHggMjRweDtcbiAgICBib3JkZXItcmFkaXVzOiAzMHB4O1xuICAgIHBvc2l0aW9uOiBhYnNvbHV0ZTtcbiAgICB0b3A6IDA7XG4gICAgbGVmdDogNTAlO1xuICAgIHRyYW5zZm9ybTogdHJhbnNsYXRlKC01MCUsIC01MCUpO1xuICB9XG4gIC5saXN0LXVuc3R5bGVkIHtcbiAgICBkaXNwbGF5OiBpbmxpbmUtYmxvY2s7XG4gICAgbGkge1xuICAgICAgZGlzcGxheTogZmxleDtcbiAgICAgIGFsaWduLWl0ZW1zOiBjZW50ZXI7XG4gICAgICArIGxpIHtcbiAgICAgICAgbWFyZ2luLXRvcDogOHB4O1xuICAgICAgfVxuICAgIH1cbiAgICAudGhlbWUtYXZ0YXIge1xuICAgICAgZGlzcGxheTogaW5saW5lLWZsZXg7XG4gICAgICB3aWR0aDogMzBweDtcbiAgICAgIGhlaWdodDogMzBweDtcbiAgICAgIGJvcmRlci1yYWRpdXM6IDEwcHg7XG4gICAgICBiYWNrZ3JvdW5kOiAjZmZmO1xuICAgICAgbWFyZ2luLXJpZ2h0OiAxNXB4O1xuICAgIH1cbiAgfVxufVxuLnNpZGUtZmVhdHVyZSB7XG4gIG92ZXJmbG93OiBoaWRkZW47XG4gIC5mZWF0dXJlLWltZy1yb3cge1xuICAgIHdpZHRoOiA4MHZ3O1xuICB9XG59XG5AbWVkaWEgb25seSBzY3JlZW4gYW5kIChtYXgtd2lkdGg6IDk5MnB4KSB7XG4gIGhlYWRlcixcbiAgc2VjdGlvbiB7XG4gICAgcGFkZGluZy10b3A6IDQwcHg7XG4gICAgcGFkZGluZy1ib3R0b206IDQwcHg7XG4gIH1cbn1cbkBtZWRpYSBvbmx5IHNjcmVlbiBhbmQgKG1heC13aWR0aDogNzY4cHgpIHtcbiAgLm5hdmJhciB7XG4gICAgaGVpZ2h0OiBhdXRvO1xuICB9XG59XG4iLCIvKio9PT09PT09PT09PT09PT09PT09PT09PT09PT09PT09PT09PT09PT09PT09PT09PT09PT09PT09PT09PT09PT09PT09PT09XG49PT09PT09PT09PT09PT09PT09PT09PT09PT09PT09PT09PT09PT09PT09PT09PT09PT09PT09PT09PT09PT09PT09PT09PT09XG5UZW1wbGF0ZSBOYW1lOiBEYXNoYm9hcmQgQm9vdHN0cmFwIDUgQWRtaW4gVGVtcGxhdGVcbkF1dGhvcjogUmFqb2RpeWEgSW5mb3RlY2hcblN1cHBvcnQ6IFtzdXBwb3J0IGxpbmtdXG5GaWxlOiBzdHlsZS5jc3Ncbj09PT09PT09PT09PT09PT09PT09PT09PT09PT09PT09PT09PT09PT09PT09PT09PT09PT09PT09PT09PT09PT09PT09PT09PT1cbj09PT09PT09PT09PT09PT09PT09PT09PT09PT09PT09PT09PT09PT09PT09PT09PT09PT09PT09PT09PT09PT09PT09PT09PT09PT09PT09PT09ICovXG5ib2R5IHtcbiAgbWFyZ2luOiAwO1xuICBvdmVyZmxvdy14OiBoaWRkZW47XG4gIGJhY2tncm91bmQ6IHdoaXRlOyB9XG5cbmEge1xuICBvdXRsaW5lOiBub25lO1xuICB0ZXh0LWRlY29yYXRpb246IG5vbmU7IH1cbiAgYTpmb2N1cywgYTpob3ZlciB7XG4gICAgb3V0bGluZTogbm9uZTtcbiAgICB0ZXh0LWRlY29yYXRpb246IG5vbmU7IH1cblxuc2VjdGlvbiB7XG4gIHBhZGRpbmctdG9wOiA5MHB4O1xuICBwYWRkaW5nLWJvdHRvbTogOTBweDsgfVxuXG4uYmctZGFyayB7XG4gIGJhY2tncm91bmQ6ICMxYzIzMmYgIWltcG9ydGFudDtcbiAgY29sb3I6ICNmZmY7IH1cbiAgLmJnLWRhcmsgcCB7XG4gICAgY29sb3I6ICNiNWJkY2E7IH1cbiAgLmJnLWRhcmsgLnRpdGxlIGgyIHtcbiAgICBjb2xvcjogcmdiYSgyNTUsIDI1NSwgMjU1LCAwLjcpOyB9XG4gICAgLmJnLWRhcmsgLnRpdGxlIGgyIHNwYW4ge1xuICAgICAgY29sb3I6ICNmZmY7IH1cblxuLnRoZW1lLWFsdC1iZyB7XG4gIGJhY2tncm91bmQ6ICNlZGVkZWQ7IH1cblxuLnRpdGxlIHtcbiAgcG9zaXRpb246IHJlbGF0aXZlO1xuICB0ZXh0LWFsaWduOiBjZW50ZXI7XG4gIG1hcmdpbi1ib3R0b206IDUwcHg7IH1cbiAgLnRpdGxlIGgyIHtcbiAgICBtYXJnaW4tYm90dG9tOiAxNXB4O1xuICAgIHotaW5kZXg6IDM7XG4gICAgY29sb3I6ICMyOTMyNDA7XG4gICAgbWFyZ2luLXRvcDogMTVweDtcbiAgICB0ZXh0LXRyYW5zZm9ybTogY2FwaXRhbGl6ZTsgfVxuICAgIC50aXRsZSBoMiBzcGFuIHtcbiAgICAgIGZvbnQtd2VpZ2h0OiA2MDA7XG4gICAgICBjb2xvcjogIzA2MDYwNjsgfVxuICAudGl0bGUgLm1hdGVyaWFsLWljb25zLXR3by10b25lIHtcbiAgICBmb250LXNpemU6IDQwcHg7IH1cbiAgQG1lZGlhIG9ubHkgc2NyZWVuIGFuZCAobWF4LXdpZHRoOiA5OTJweCkge1xuICAgIC50aXRsZSB7XG4gICAgICBtYXJnaW4tYm90dG9tOiAzMHB4OyB9XG4gICAgICAudGl0bGUgaDIge1xuICAgICAgICBtYXJnaW4tYm90dG9tOiAxMHB4OyB9IH1cblxuLm5hdmJhciB7XG4gIHBvc2l0aW9uOiBmaXhlZDtcbiAgei1pbmRleDogMTAwMDtcbiAgd2lkdGg6IDEwMCU7XG4gIGhlaWdodDogNzBweDtcbiAgdG9wOiAtODBweDtcbiAgYmFja2Ryb3AtZmlsdGVyOiBibHVyKDVweCk7XG4gIHRyYW5zaXRpb246IGFsbCAwLjNzIGxpbmVhcjtcbiAgbWFyZ2luLWJvdHRvbTogMDtcbiAgYm94LXNoYWRvdzogMCA4cHggNnB4IC0xMHB4IHJnYmEoMCwgMCwgMCwgMC41KTsgfVxuICAubmF2YmFyLmRlZmF1bHQge1xuICAgIHRvcDogMDtcbiAgICBiYWNrZ3JvdW5kOiByZ2JhKDI4LCAzNSwgNDcsIDAuOSk7IH1cbiAgLm5hdmJhci5kZWZhdWx0LnRvcC1uYXYtY29sbGFwc2Uge1xuICAgIGJhY2tncm91bmQ6IHRyYW5zcGFyZW50O1xuICAgIGJveC1zaGFkb3c6IG5vbmU7IH1cbiAgQG1lZGlhIChtaW4td2lkdGg6IDc2N3B4KSB7XG4gICAgLm5hdmJhciAubmF2YmFyLW5hdiAubmF2LWxpbmsge1xuICAgICAgcG9zaXRpb246IHJlbGF0aXZlOyB9XG4gICAgICAubmF2YmFyIC5uYXZiYXItbmF2IC5uYXYtbGluay5hY3RpdmU6YWZ0ZXIge1xuICAgICAgICBsZWZ0OiAxNSU7XG4gICAgICAgIHJpZ2h0OiAxNSU7IH0gfVxuXG5oZWFkZXI6bm90KC5wcmljZS1oZWFkZXIpIHtcbiAgcG9zaXRpb246IHJlbGF0aXZlO1xuICBjb2xvcjogI2ZmZjtcbiAgbWluLWhlaWdodDogNjB2aDtcbiAgcGFkZGluZy10b3A6IDE2MHB4O1xuICBwYWRkaW5nLWJvdHRvbTogOTBweDtcbiAgZGlzcGxheTogZmxleDtcbiAgYWxpZ24taXRlbXM6IGNlbnRlcjsgfVxuICBoZWFkZXI6bm90KC5wcmljZS1oZWFkZXIpID4gKiB7XG4gICAgcG9zaXRpb246IHJlbGF0aXZlO1xuICAgIHotaW5kZXg6IDU7IH1cbiAgaGVhZGVyOm5vdCgucHJpY2UtaGVhZGVyKTphZnRlciwgaGVhZGVyOm5vdCgucHJpY2UtaGVhZGVyKTpiZWZvcmUge1xuICAgIGNvbnRlbnQ6IFwiXCI7XG4gICAgcG9zaXRpb246IGFic29sdXRlO1xuICAgIHRvcDogLTEwMHB4O1xuICAgIGxlZnQ6IDA7XG4gICAgcmlnaHQ6IDA7XG4gICAgYm90dG9tOiAwOyB9XG4gIGhlYWRlcjpub3QoLnByaWNlLWhlYWRlcik6YmVmb3JlIHtcbiAgICBiYWNrZ3JvdW5kLWF0dGFjaG1lbnQ6IGZpeGVkO1xuICAgIHotaW5kZXg6IDE7IH1cblxuaGVhZGVyLnByaWNlLWhlYWRlciB7XG4gIHBvc2l0aW9uOiByZWxhdGl2ZTtcbiAgbWluLWhlaWdodDogNjB2aDtcbiAgcGFkZGluZy10b3A6IDE2MHB4O1xuICBwYWRkaW5nLWJvdHRvbTogOTBweDtcbiAgZGlzcGxheTogZmxleDtcbiAgYWxpZ24taXRlbXM6IGNlbnRlcjsgfVxuXG4uZGFzaGJvYXJkLWJsb2NrIHtcbiAgcG9zaXRpb246IHJlbGF0aXZlO1xuICBvdmVyZmxvdzogaGlkZGVuOyB9XG4gIC5kYXNoYm9hcmQtYmxvY2sgLmltZy1kYXNoYm9hcmQge1xuICAgIG1hcmdpbi1ib3R0b206IC0xNSU7IH1cblxuLmZlYXR1cmUge1xuICB0ZXh0LWFsaWduOiBjZW50ZXI7IH1cbiAgLmZlYXR1cmUgLmNhcmQtYm9keSB7XG4gICAgcGFkZGluZy10b3A6IDQwcHg7XG4gICAgcGFkZGluZy1ib3R0b206IDQwcHg7IH1cbiAgLmZlYXR1cmUgLnRoZW1lLWF2dGFyIHtcbiAgICB3aWR0aDogMTMwcHg7XG4gICAgaGVpZ2h0OiAxMzBweDtcbiAgICBmb250LXNpemU6IDQ1cHg7XG4gICAgbWFyZ2luOiAwIGF1dG87XG4gICAgYm9yZGVyLXJhZGl1czogNTBweDsgfVxuXG4ucHJpY2Utc2VjdGlvbiB7XG4gIHBvc2l0aW9uOiByZWxhdGl2ZTsgfVxuICAucHJpY2Utc2VjdGlvbiA+ICoge1xuICAgIHBvc2l0aW9uOiByZWxhdGl2ZTtcbiAgICB6LWluZGV4OiA1OyB9XG4gIC5wcmljZS1zZWN0aW9uOjphZnRlciB7XG4gICAgY29udGVudDogXCJcIjtcbiAgICBwb3NpdGlvbjogYWJzb2x1dGU7XG4gICAgbGVmdDogMDtcbiAgICByaWdodDogMDtcbiAgICBib3R0b206IDA7XG4gICAgdG9wOiA1MCU7XG4gICAgei1pbmRleDogMTtcbiAgICBiYWNrZ3JvdW5kOiBsaW5lYXItZ3JhZGllbnQoMTgwZGVnLCAjY2VjZWNlIDAlLCAjZmZmZmZmIDEwMCUpOyB9XG5cbi5mYXEgLmFjY29yZGlvbiAuYWNjb3JkaW9uLWl0ZW0ge1xuICBib3JkZXItcmFkaXVzOiAxMHB4O1xuICBvdmVyZmxvdzogaGlkZGVuO1xuICBib3JkZXI6IG5vbmU7XG4gIG1hcmdpbi1ib3R0b206IDEwcHg7IH1cbiAgLmZhcSAuYWNjb3JkaW9uIC5hY2NvcmRpb24taXRlbSAuYWNjb3JkaW9uLWJ1dHRvbiB7XG4gICAgZm9udC13ZWlnaHQ6IDcwMDtcbiAgICBwYWRkaW5nOiAxLjNyZW0gMS4yNXJlbTsgfVxuICAgIC5mYXEgLmFjY29yZGlvbiAuYWNjb3JkaW9uLWl0ZW0gLmFjY29yZGlvbi1idXR0b24gc3BhbiA+IGkge1xuICAgICAgZm9udC1zaXplOiAyMHB4O1xuICAgICAgbWFyZ2luLXJpZ2h0OiA4cHg7IH1cbiAgICAuZmFxIC5hY2NvcmRpb24gLmFjY29yZGlvbi1pdGVtIC5hY2NvcmRpb24tYnV0dG9uOm5vdCguY29sbGFwc2VkKSB7XG4gICAgICBib3JkZXItcmFkaXVzOiAxMHB4O1xuICAgICAgYmFja2dyb3VuZDogdHJhbnNwYXJlbnQ7XG4gICAgICBib3gtc2hhZG93OiAwIDZweCAzMHB4IHJnYmEoMTgyLCAxODYsIDIwMywgMC4zKTsgfVxuICAuZmFxIC5hY2NvcmRpb24gLmFjY29yZGlvbi1pdGVtIC5hY2NvcmRpb24tYm9keSB7XG4gICAgcGFkZGluZzogMi4zcmVtIDIuM3JlbSAyLjNyZW0gM3JlbTsgfVxuXG4ucHJpY2UtY2FyZCB7XG4gIHRleHQtYWxpZ246IGNlbnRlcjtcbiAgcG9zaXRpb246IHJlbGF0aXZlO1xuICBtYXJnaW4tdG9wOiAzMHB4OyB9XG4gIC5wcmljZS1jYXJkLnByaWNlLTIge1xuICAgIGNvbG9yOiAjZmZmOyB9XG4gICAgLnByaWNlLWNhcmQucHJpY2UtMiAucHJpY2UtYmFkZ2Uge1xuICAgICAgY29sb3I6ICNmZmY7XG4gICAgICBiYWNrZ3JvdW5kOiAjMWMyMzJmOyB9XG4gIC5wcmljZS1jYXJkIC5wLXByaWNlIHtcbiAgICBmb250LXNpemU6IDgwcHg7IH1cbiAgLnByaWNlLWNhcmQgLnByaWNlLWJhZGdlIHtcbiAgICBjb2xvcjogI2ZmZjtcbiAgICBwYWRkaW5nOiA3cHggMjRweDtcbiAgICBib3JkZXItcmFkaXVzOiAzMHB4O1xuICAgIHBvc2l0aW9uOiBhYnNvbHV0ZTtcbiAgICB0b3A6IDA7XG4gICAgbGVmdDogNTAlO1xuICAgIHRyYW5zZm9ybTogdHJhbnNsYXRlKC01MCUsIC01MCUpOyB9XG4gIC5wcmljZS1jYXJkIC5saXN0LXVuc3R5bGVkIHtcbiAgICBkaXNwbGF5OiBpbmxpbmUtYmxvY2s7IH1cbiAgICAucHJpY2UtY2FyZCAubGlzdC11bnN0eWxlZCBsaSB7XG4gICAgICBkaXNwbGF5OiBmbGV4O1xuICAgICAgYWxpZ24taXRlbXM6IGNlbnRlcjsgfVxuICAgICAgLnByaWNlLWNhcmQgLmxpc3QtdW5zdHlsZWQgbGkgKyBsaSB7XG4gICAgICAgIG1hcmdpbi10b3A6IDhweDsgfVxuICAgIC5wcmljZS1jYXJkIC5saXN0LXVuc3R5bGVkIC50aGVtZS1hdnRhciB7XG4gICAgICBkaXNwbGF5OiBpbmxpbmUtZmxleDtcbiAgICAgIHdpZHRoOiAzMHB4O1xuICAgICAgaGVpZ2h0OiAzMHB4O1xuICAgICAgYm9yZGVyLXJhZGl1czogMTBweDtcbiAgICAgIGJhY2tncm91bmQ6ICNmZmY7XG4gICAgICBtYXJnaW4tcmlnaHQ6IDE1cHg7IH1cblxuLnNpZGUtZmVhdHVyZSB7XG4gIG92ZXJmbG93OiBoaWRkZW47IH1cbiAgLnNpZGUtZmVhdHVyZSAuZmVhdHVyZS1pbWctcm93IHtcbiAgICB3aWR0aDogODB2dzsgfVxuXG5AbWVkaWEgb25seSBzY3JlZW4gYW5kIChtYXgtd2lkdGg6IDk5MnB4KSB7XG4gIGhlYWRlcixcbiAgc2VjdGlvbiB7XG4gICAgcGFkZGluZy10b3A6IDQwcHg7XG4gICAgcGFkZGluZy1ib3R0b206IDQwcHg7IH0gfVxuXG5AbWVkaWEgb25seSBzY3JlZW4gYW5kIChtYXgtd2lkdGg6IDc2OHB4KSB7XG4gIC5uYXZiYXIge1xuICAgIGhlaWdodDogYXV0bzsgfSB9XG4iLCIvLyBWYXJpYWJsZXNcbi8vXG4vLyBWYXJpYWJsZXMgc2hvdWxkIGZvbGxvdyB0aGUgYCRjb21wb25lbnQtc3RhdGUtcHJvcGVydHktc2l6ZWAgZm9ybXVsYSBmb3Jcbi8vIGNvbnNpc3RlbnQgbmFtaW5nLiBFeDogJG5hdi1saW5rLWRpc2FibGVkLWNvbG9yIGFuZCAkbW9kYWwtY29udGVudC1ib3gtc2hhZG93LXhzLlxuXG4vLyBDb2xvciBzeXN0ZW1cblxuJHdoaXRlOiAjZmZmZmZmO1xuJGdyYXktMTAwOiAjZjhmOWZhO1xuJGdyYXktMjAwOiAjZTllY2VmO1xuJGdyYXktMzAwOiAjZGVlMmU2O1xuJGdyYXktNDAwOiAjY2VkNGRhO1xuJGdyYXktNTAwOiAjYWRiNWJkO1xuJGdyYXktNjAwOiAjNmM3NTdkO1xuJGdyYXktNzAwOiAjNDk1MDU3O1xuJGdyYXktODAwOiAjMzQzYTQwO1xuJGdyYXktOTAwOiAjMjEyNTI5O1xuJGJsYWNrOiAjMDAwMDAwO1xuXG4vLyBmdXN2LWRpc2FibGVcbiRncmF5czogKFxuICBcIjEwMFwiOiAkZ3JheS0xMDAsXG4gIFwiMjAwXCI6ICRncmF5LTIwMCxcbiAgXCIzMDBcIjogJGdyYXktMzAwLFxuICBcIjQwMFwiOiAkZ3JheS00MDAsXG4gIFwiNTAwXCI6ICRncmF5LTUwMCxcbiAgXCI2MDBcIjogJGdyYXktNjAwLFxuICBcIjcwMFwiOiAkZ3JheS03MDAsXG4gIFwiODAwXCI6ICRncmF5LTgwMCxcbiAgXCI5MDBcIjogJGdyYXktOTAwLFxuKTtcbi8vIGZ1c3YtZW5hYmxlXG5cbiRibHVlOiAjNTE0NTlkOyAvLyBjaGFuZ2VcbiRpbmRpZ286ICM2NjEwZjI7XG4kcHVycGxlOiAjNmY0MmMxO1xuJHBpbms6ICNlODNlOGM7XG4kcmVkOiAjZmYzYTZlOyAvLyBjaGFuZ2VcbiRvcmFuZ2U6ICNmZDdlMTQ7XG4keWVsbG93OiAjZmZhMjFkOyAvLyBjaGFuZ2VcbiRncmVlbjogIzZmZDk0MzsgLy8gY2hhbmdlXG4kdGVhbDogIzIwYzk5NztcbiRjeWFuOiAjM2VjOWQ2OyAvLyBjaGFuZ2VcblxuLy8gc2Nzcy1kb2NzLXN0YXJ0IGNvbG9ycy1tYXBcbiRjb2xvcnM6IChcbiAgXCJibHVlXCI6ICRibHVlLFxuICBcImluZGlnb1wiOiAkaW5kaWdvLFxuICBcInB1cnBsZVwiOiAkcHVycGxlLFxuICBcInBpbmtcIjogJHBpbmssXG4gIFwicmVkXCI6ICRyZWQsXG4gIFwib3JhbmdlXCI6ICRvcmFuZ2UsXG4gIFwieWVsbG93XCI6ICR5ZWxsb3csXG4gIFwiZ3JlZW5cIjogJGdyZWVuLFxuICBcInRlYWxcIjogJHRlYWwsXG4gIFwiY3lhblwiOiAkY3lhbixcbiAgXCJ3aGl0ZVwiOiAkd2hpdGUsXG4gIFwiZ3JheVwiOiAkZ3JheS02MDAsXG4gIFwiZ3JheS1kYXJrXCI6ICRncmF5LTgwMCxcbik7XG4vLyBzY3NzLWRvY3MtZW5kIGNvbG9ycy1tYXBcblxuJHByaW1hcnk6ICRibHVlOyAvLyBjaGFuZ2VcbiRzZWNvbmRhcnk6ICRncmF5LTYwMDsgLy8gY2hhbmdlXG4kc3VjY2VzczogJGdyZWVuOyAvLyBjaGFuZ2VcbiRpbmZvOiAkY3lhbjsgLy8gY2hhbmdlXG4kd2FybmluZzogJHllbGxvdzsgLy8gY2hhbmdlXG4kZGFuZ2VyOiAkcmVkOyAvLyBjaGFuZ2VcbiRsaWdodDogJGdyYXktMTAwOyAvLyBjaGFuZ2VcbiRkYXJrOiAjMWMyMzJmOyAvLyBjaGFuZ2VcblxuLy8gc2Nzcy1kb2NzLXN0YXJ0IHRoZW1lLWNvbG9ycy1tYXBcbiR0aGVtZS1jb2xvcnM6IChcbiAgXCJwcmltYXJ5XCI6ICRwcmltYXJ5LFxuICBcInNlY29uZGFyeVwiOiAkc2Vjb25kYXJ5LFxuICBcInN1Y2Nlc3NcIjogJHN1Y2Nlc3MsXG4gIFwiaW5mb1wiOiAkaW5mbyxcbiAgXCJ3YXJuaW5nXCI6ICR3YXJuaW5nLFxuICBcImRhbmdlclwiOiAkZGFuZ2VyLFxuICBcImxpZ2h0XCI6ICRsaWdodCxcbiAgXCJkYXJrXCI6ICRkYXJrLFxuKTtcbi8vIHNjc3MtZG9jcy1lbmQgdGhlbWUtY29sb3JzLW1hcFxuXG4vLyBzY3NzLWRvY3Mtc3RhcnQgdGhlbWUtY29sb3JzLXJnYlxuJHRoZW1lLWNvbG9ycy1yZ2I6IG1hcC1sb29wKCR0aGVtZS1jb2xvcnMsIHRvLXJnYiwgXCIkdmFsdWVcIik7XG4vLyBzY3NzLWRvY3MtZW5kIHRoZW1lLWNvbG9ycy1yZ2JcblxuLy8gVGhlIGNvbnRyYXN0IHJhdGlvIHRvIHJlYWNoIGFnYWluc3Qgd2hpdGUsIHRvIGRldGVybWluZSBpZiBjb2xvciBjaGFuZ2VzIGZyb20gXCJsaWdodFwiIHRvIFwiZGFya1wiLiBBY2NlcHRhYmxlIHZhbHVlcyBmb3IgV0NBRyAyLjAgYXJlIDMsIDQuNSBhbmQgNy5cbi8vIFNlZSBodHRwczovL3d3dy53My5vcmcvVFIvV0NBRzIwLyN2aXN1YWwtYXVkaW8tY29udHJhc3QtY29udHJhc3RcbiRtaW4tY29udHJhc3QtcmF0aW86IDEuNTU7XG5cbi8vIEN1c3RvbWl6ZSB0aGUgbGlnaHQgYW5kIGRhcmsgdGV4dCBjb2xvcnMgZm9yIHVzZSBpbiBvdXIgY29sb3IgY29udHJhc3QgZnVuY3Rpb24uXG4kY29sb3ItY29udHJhc3QtZGFyazogJGdyYXktOTAwO1xuJGNvbG9yLWNvbnRyYXN0LWxpZ2h0OiAkd2hpdGU7XG5cbi8vIGZ1c3YtZGlzYWJsZVxuJGJsdWUtMTAwOiB0aW50LWNvbG9yKCRibHVlLCA4MCUpO1xuJGJsdWUtMjAwOiB0aW50LWNvbG9yKCRibHVlLCA2MCUpO1xuJGJsdWUtMzAwOiB0aW50LWNvbG9yKCRibHVlLCA0MCUpO1xuJGJsdWUtNDAwOiB0aW50LWNvbG9yKCRibHVlLCAyMCUpO1xuJGJsdWUtNTAwOiAkYmx1ZTtcbiRibHVlLTYwMDogc2hhZGUtY29sb3IoJGJsdWUsIDIwJSk7XG4kYmx1ZS03MDA6IHNoYWRlLWNvbG9yKCRibHVlLCA0MCUpO1xuJGJsdWUtODAwOiBzaGFkZS1jb2xvcigkYmx1ZSwgNjAlKTtcbiRibHVlLTkwMDogc2hhZGUtY29sb3IoJGJsdWUsIDgwJSk7XG5cbiRpbmRpZ28tMTAwOiB0aW50LWNvbG9yKCRpbmRpZ28sIDgwJSk7XG4kaW5kaWdvLTIwMDogdGludC1jb2xvcigkaW5kaWdvLCA2MCUpO1xuJGluZGlnby0zMDA6IHRpbnQtY29sb3IoJGluZGlnbywgNDAlKTtcbiRpbmRpZ28tNDAwOiB0aW50LWNvbG9yKCRpbmRpZ28sIDIwJSk7XG4kaW5kaWdvLTUwMDogJGluZGlnbztcbiRpbmRpZ28tNjAwOiBzaGFkZS1jb2xvcigkaW5kaWdvLCAyMCUpO1xuJGluZGlnby03MDA6IHNoYWRlLWNvbG9yKCRpbmRpZ28sIDQwJSk7XG4kaW5kaWdvLTgwMDogc2hhZGUtY29sb3IoJGluZGlnbywgNjAlKTtcbiRpbmRpZ28tOTAwOiBzaGFkZS1jb2xvcigkaW5kaWdvLCA4MCUpO1xuXG4kcHVycGxlLTEwMDogdGludC1jb2xvcigkcHVycGxlLCA4MCUpO1xuJHB1cnBsZS0yMDA6IHRpbnQtY29sb3IoJHB1cnBsZSwgNjAlKTtcbiRwdXJwbGUtMzAwOiB0aW50LWNvbG9yKCRwdXJwbGUsIDQwJSk7XG4kcHVycGxlLTQwMDogdGludC1jb2xvcigkcHVycGxlLCAyMCUpO1xuJHB1cnBsZS01MDA6ICRwdXJwbGU7XG4kcHVycGxlLTYwMDogc2hhZGUtY29sb3IoJHB1cnBsZSwgMjAlKTtcbiRwdXJwbGUtNzAwOiBzaGFkZS1jb2xvcigkcHVycGxlLCA0MCUpO1xuJHB1cnBsZS04MDA6IHNoYWRlLWNvbG9yKCRwdXJwbGUsIDYwJSk7XG4kcHVycGxlLTkwMDogc2hhZGUtY29sb3IoJHB1cnBsZSwgODAlKTtcblxuJHBpbmstMTAwOiB0aW50LWNvbG9yKCRwaW5rLCA4MCUpO1xuJHBpbmstMjAwOiB0aW50LWNvbG9yKCRwaW5rLCA2MCUpO1xuJHBpbmstMzAwOiB0aW50LWNvbG9yKCRwaW5rLCA0MCUpO1xuJHBpbmstNDAwOiB0aW50LWNvbG9yKCRwaW5rLCAyMCUpO1xuJHBpbmstNTAwOiAkcGluaztcbiRwaW5rLTYwMDogc2hhZGUtY29sb3IoJHBpbmssIDIwJSk7XG4kcGluay03MDA6IHNoYWRlLWNvbG9yKCRwaW5rLCA0MCUpO1xuJHBpbmstODAwOiBzaGFkZS1jb2xvcigkcGluaywgNjAlKTtcbiRwaW5rLTkwMDogc2hhZGUtY29sb3IoJHBpbmssIDgwJSk7XG5cbiRyZWQtMTAwOiB0aW50LWNvbG9yKCRyZWQsIDgwJSk7XG4kcmVkLTIwMDogdGludC1jb2xvcigkcmVkLCA2MCUpO1xuJHJlZC0zMDA6IHRpbnQtY29sb3IoJHJlZCwgNDAlKTtcbiRyZWQtNDAwOiB0aW50LWNvbG9yKCRyZWQsIDIwJSk7XG4kcmVkLTUwMDogJHJlZDtcbiRyZWQtNjAwOiBzaGFkZS1jb2xvcigkcmVkLCAyMCUpO1xuJHJlZC03MDA6IHNoYWRlLWNvbG9yKCRyZWQsIDQwJSk7XG4kcmVkLTgwMDogc2hhZGUtY29sb3IoJHJlZCwgNjAlKTtcbiRyZWQtOTAwOiBzaGFkZS1jb2xvcigkcmVkLCA4MCUpO1xuXG4kb3JhbmdlLTEwMDogdGludC1jb2xvcigkb3JhbmdlLCA4MCUpO1xuJG9yYW5nZS0yMDA6IHRpbnQtY29sb3IoJG9yYW5nZSwgNjAlKTtcbiRvcmFuZ2UtMzAwOiB0aW50LWNvbG9yKCRvcmFuZ2UsIDQwJSk7XG4kb3JhbmdlLTQwMDogdGludC1jb2xvcigkb3JhbmdlLCAyMCUpO1xuJG9yYW5nZS01MDA6ICRvcmFuZ2U7XG4kb3JhbmdlLTYwMDogc2hhZGUtY29sb3IoJG9yYW5nZSwgMjAlKTtcbiRvcmFuZ2UtNzAwOiBzaGFkZS1jb2xvcigkb3JhbmdlLCA0MCUpO1xuJG9yYW5nZS04MDA6IHNoYWRlLWNvbG9yKCRvcmFuZ2UsIDYwJSk7XG4kb3JhbmdlLTkwMDogc2hhZGUtY29sb3IoJG9yYW5nZSwgODAlKTtcblxuJHllbGxvdy0xMDA6IHRpbnQtY29sb3IoJHllbGxvdywgODAlKTtcbiR5ZWxsb3ctMjAwOiB0aW50LWNvbG9yKCR5ZWxsb3csIDYwJSk7XG4keWVsbG93LTMwMDogdGludC1jb2xvcigkeWVsbG93LCA0MCUpO1xuJHllbGxvdy00MDA6IHRpbnQtY29sb3IoJHllbGxvdywgMjAlKTtcbiR5ZWxsb3ctNTAwOiAkeWVsbG93O1xuJHllbGxvdy02MDA6IHNoYWRlLWNvbG9yKCR5ZWxsb3csIDIwJSk7XG4keWVsbG93LTcwMDogc2hhZGUtY29sb3IoJHllbGxvdywgNDAlKTtcbiR5ZWxsb3ctODAwOiBzaGFkZS1jb2xvcigkeWVsbG93LCA2MCUpO1xuJHllbGxvdy05MDA6IHNoYWRlLWNvbG9yKCR5ZWxsb3csIDgwJSk7XG5cbiRncmVlbi0xMDA6IHRpbnQtY29sb3IoJGdyZWVuLCA4MCUpO1xuJGdyZWVuLTIwMDogdGludC1jb2xvcigkZ3JlZW4sIDYwJSk7XG4kZ3JlZW4tMzAwOiB0aW50LWNvbG9yKCRncmVlbiwgNDAlKTtcbiRncmVlbi00MDA6IHRpbnQtY29sb3IoJGdyZWVuLCAyMCUpO1xuJGdyZWVuLTUwMDogJGdyZWVuO1xuJGdyZWVuLTYwMDogc2hhZGUtY29sb3IoJGdyZWVuLCAyMCUpO1xuJGdyZWVuLTcwMDogc2hhZGUtY29sb3IoJGdyZWVuLCA0MCUpO1xuJGdyZWVuLTgwMDogc2hhZGUtY29sb3IoJGdyZWVuLCA2MCUpO1xuJGdyZWVuLTkwMDogc2hhZGUtY29sb3IoJGdyZWVuLCA4MCUpO1xuXG4kdGVhbC0xMDA6IHRpbnQtY29sb3IoJHRlYWwsIDgwJSk7XG4kdGVhbC0yMDA6IHRpbnQtY29sb3IoJHRlYWwsIDYwJSk7XG4kdGVhbC0zMDA6IHRpbnQtY29sb3IoJHRlYWwsIDQwJSk7XG4kdGVhbC00MDA6IHRpbnQtY29sb3IoJHRlYWwsIDIwJSk7XG4kdGVhbC01MDA6ICR0ZWFsO1xuJHRlYWwtNjAwOiBzaGFkZS1jb2xvcigkdGVhbCwgMjAlKTtcbiR0ZWFsLTcwMDogc2hhZGUtY29sb3IoJHRlYWwsIDQwJSk7XG4kdGVhbC04MDA6IHNoYWRlLWNvbG9yKCR0ZWFsLCA2MCUpO1xuJHRlYWwtOTAwOiBzaGFkZS1jb2xvcigkdGVhbCwgODAlKTtcblxuJGN5YW4tMTAwOiB0aW50LWNvbG9yKCRjeWFuLCA4MCUpO1xuJGN5YW4tMjAwOiB0aW50LWNvbG9yKCRjeWFuLCA2MCUpO1xuJGN5YW4tMzAwOiB0aW50LWNvbG9yKCRjeWFuLCA0MCUpO1xuJGN5YW4tNDAwOiB0aW50LWNvbG9yKCRjeWFuLCAyMCUpO1xuJGN5YW4tNTAwOiAkY3lhbjtcbiRjeWFuLTYwMDogc2hhZGUtY29sb3IoJGN5YW4sIDIwJSk7XG4kY3lhbi03MDA6IHNoYWRlLWNvbG9yKCRjeWFuLCA0MCUpO1xuJGN5YW4tODAwOiBzaGFkZS1jb2xvcigkY3lhbiwgNjAlKTtcbiRjeWFuLTkwMDogc2hhZGUtY29sb3IoJGN5YW4sIDgwJSk7XG5cbiRibHVlczogKFxuICBcImJsdWUtMTAwXCI6ICRibHVlLTEwMCxcbiAgXCJibHVlLTIwMFwiOiAkYmx1ZS0yMDAsXG4gIFwiYmx1ZS0zMDBcIjogJGJsdWUtMzAwLFxuICBcImJsdWUtNDAwXCI6ICRibHVlLTQwMCxcbiAgXCJibHVlLTUwMFwiOiAkYmx1ZS01MDAsXG4gIFwiYmx1ZS02MDBcIjogJGJsdWUtNjAwLFxuICBcImJsdWUtNzAwXCI6ICRibHVlLTcwMCxcbiAgXCJibHVlLTgwMFwiOiAkYmx1ZS04MDAsXG4gIFwiYmx1ZS05MDBcIjogJGJsdWUtOTAwLFxuKTtcblxuJGluZGlnb3M6IChcbiAgXCJpbmRpZ28tMTAwXCI6ICRpbmRpZ28tMTAwLFxuICBcImluZGlnby0yMDBcIjogJGluZGlnby0yMDAsXG4gIFwiaW5kaWdvLTMwMFwiOiAkaW5kaWdvLTMwMCxcbiAgXCJpbmRpZ28tNDAwXCI6ICRpbmRpZ28tNDAwLFxuICBcImluZGlnby01MDBcIjogJGluZGlnby01MDAsXG4gIFwiaW5kaWdvLTYwMFwiOiAkaW5kaWdvLTYwMCxcbiAgXCJpbmRpZ28tNzAwXCI6ICRpbmRpZ28tNzAwLFxuICBcImluZGlnby04MDBcIjogJGluZGlnby04MDAsXG4gIFwiaW5kaWdvLTkwMFwiOiAkaW5kaWdvLTkwMCxcbik7XG5cbiRwdXJwbGVzOiAoXG4gIFwicHVycGxlLTEwMFwiOiAkcHVycGxlLTIwMCxcbiAgXCJwdXJwbGUtMjAwXCI6ICRwdXJwbGUtMTAwLFxuICBcInB1cnBsZS0zMDBcIjogJHB1cnBsZS0zMDAsXG4gIFwicHVycGxlLTQwMFwiOiAkcHVycGxlLTQwMCxcbiAgXCJwdXJwbGUtNTAwXCI6ICRwdXJwbGUtNTAwLFxuICBcInB1cnBsZS02MDBcIjogJHB1cnBsZS02MDAsXG4gIFwicHVycGxlLTcwMFwiOiAkcHVycGxlLTcwMCxcbiAgXCJwdXJwbGUtODAwXCI6ICRwdXJwbGUtODAwLFxuICBcInB1cnBsZS05MDBcIjogJHB1cnBsZS05MDAsXG4pO1xuXG4kcGlua3M6IChcbiAgXCJwaW5rLTEwMFwiOiAkcGluay0xMDAsXG4gIFwicGluay0yMDBcIjogJHBpbmstMjAwLFxuICBcInBpbmstMzAwXCI6ICRwaW5rLTMwMCxcbiAgXCJwaW5rLTQwMFwiOiAkcGluay00MDAsXG4gIFwicGluay01MDBcIjogJHBpbmstNTAwLFxuICBcInBpbmstNjAwXCI6ICRwaW5rLTYwMCxcbiAgXCJwaW5rLTcwMFwiOiAkcGluay03MDAsXG4gIFwicGluay04MDBcIjogJHBpbmstODAwLFxuICBcInBpbmstOTAwXCI6ICRwaW5rLTkwMCxcbik7XG5cbiRyZWRzOiAoXG4gIFwicmVkLTEwMFwiOiAkcmVkLTEwMCxcbiAgXCJyZWQtMjAwXCI6ICRyZWQtMjAwLFxuICBcInJlZC0zMDBcIjogJHJlZC0zMDAsXG4gIFwicmVkLTQwMFwiOiAkcmVkLTQwMCxcbiAgXCJyZWQtNTAwXCI6ICRyZWQtNTAwLFxuICBcInJlZC02MDBcIjogJHJlZC02MDAsXG4gIFwicmVkLTcwMFwiOiAkcmVkLTcwMCxcbiAgXCJyZWQtODAwXCI6ICRyZWQtODAwLFxuICBcInJlZC05MDBcIjogJHJlZC05MDAsXG4pO1xuXG4kb3JhbmdlczogKFxuICBcIm9yYW5nZS0xMDBcIjogJG9yYW5nZS0xMDAsXG4gIFwib3JhbmdlLTIwMFwiOiAkb3JhbmdlLTIwMCxcbiAgXCJvcmFuZ2UtMzAwXCI6ICRvcmFuZ2UtMzAwLFxuICBcIm9yYW5nZS00MDBcIjogJG9yYW5nZS00MDAsXG4gIFwib3JhbmdlLTUwMFwiOiAkb3JhbmdlLTUwMCxcbiAgXCJvcmFuZ2UtNjAwXCI6ICRvcmFuZ2UtNjAwLFxuICBcIm9yYW5nZS03MDBcIjogJG9yYW5nZS03MDAsXG4gIFwib3JhbmdlLTgwMFwiOiAkb3JhbmdlLTgwMCxcbiAgXCJvcmFuZ2UtOTAwXCI6ICRvcmFuZ2UtOTAwLFxuKTtcblxuJHllbGxvd3M6IChcbiAgXCJ5ZWxsb3ctMTAwXCI6ICR5ZWxsb3ctMTAwLFxuICBcInllbGxvdy0yMDBcIjogJHllbGxvdy0yMDAsXG4gIFwieWVsbG93LTMwMFwiOiAkeWVsbG93LTMwMCxcbiAgXCJ5ZWxsb3ctNDAwXCI6ICR5ZWxsb3ctNDAwLFxuICBcInllbGxvdy01MDBcIjogJHllbGxvdy01MDAsXG4gIFwieWVsbG93LTYwMFwiOiAkeWVsbG93LTYwMCxcbiAgXCJ5ZWxsb3ctNzAwXCI6ICR5ZWxsb3ctNzAwLFxuICBcInllbGxvdy04MDBcIjogJHllbGxvdy04MDAsXG4gIFwieWVsbG93LTkwMFwiOiAkeWVsbG93LTkwMCxcbik7XG5cbiRncmVlbnM6IChcbiAgXCJncmVlbi0xMDBcIjogJGdyZWVuLTEwMCxcbiAgXCJncmVlbi0yMDBcIjogJGdyZWVuLTIwMCxcbiAgXCJncmVlbi0zMDBcIjogJGdyZWVuLTMwMCxcbiAgXCJncmVlbi00MDBcIjogJGdyZWVuLTQwMCxcbiAgXCJncmVlbi01MDBcIjogJGdyZWVuLTUwMCxcbiAgXCJncmVlbi02MDBcIjogJGdyZWVuLTYwMCxcbiAgXCJncmVlbi03MDBcIjogJGdyZWVuLTcwMCxcbiAgXCJncmVlbi04MDBcIjogJGdyZWVuLTgwMCxcbiAgXCJncmVlbi05MDBcIjogJGdyZWVuLTkwMCxcbik7XG5cbiR0ZWFsczogKFxuICBcInRlYWwtMTAwXCI6ICR0ZWFsLTEwMCxcbiAgXCJ0ZWFsLTIwMFwiOiAkdGVhbC0yMDAsXG4gIFwidGVhbC0zMDBcIjogJHRlYWwtMzAwLFxuICBcInRlYWwtNDAwXCI6ICR0ZWFsLTQwMCxcbiAgXCJ0ZWFsLTUwMFwiOiAkdGVhbC01MDAsXG4gIFwidGVhbC02MDBcIjogJHRlYWwtNjAwLFxuICBcInRlYWwtNzAwXCI6ICR0ZWFsLTcwMCxcbiAgXCJ0ZWFsLTgwMFwiOiAkdGVhbC04MDAsXG4gIFwidGVhbC05MDBcIjogJHRlYWwtOTAwLFxuKTtcblxuJGN5YW5zOiAoXG4gIFwiY3lhbi0xMDBcIjogJGN5YW4tMTAwLFxuICBcImN5YW4tMjAwXCI6ICRjeWFuLTIwMCxcbiAgXCJjeWFuLTMwMFwiOiAkY3lhbi0zMDAsXG4gIFwiY3lhbi00MDBcIjogJGN5YW4tNDAwLFxuICBcImN5YW4tNTAwXCI6ICRjeWFuLTUwMCxcbiAgXCJjeWFuLTYwMFwiOiAkY3lhbi02MDAsXG4gIFwiY3lhbi03MDBcIjogJGN5YW4tNzAwLFxuICBcImN5YW4tODAwXCI6ICRjeWFuLTgwMCxcbiAgXCJjeWFuLTkwMFwiOiAkY3lhbi05MDAsXG4pO1xuLy8gZnVzdi1lbmFibGVcblxuLy8gQ2hhcmFjdGVycyB3aGljaCBhcmUgZXNjYXBlZCBieSB0aGUgZXNjYXBlLXN2ZyBmdW5jdGlvblxuJGVzY2FwZWQtY2hhcmFjdGVyczogKFxuICAoXCI8XCIsIFwiJTNjXCIpLFxuICAoXCI+XCIsIFwiJTNlXCIpLFxuICAoXCIjXCIsIFwiJTIzXCIpLFxuICAoXCIoXCIsIFwiJTI4XCIpLFxuICAoXCIpXCIsIFwiJTI5XCIpXG4pO1xuXG4vLyBPcHRpb25zXG4vL1xuLy8gUXVpY2tseSBtb2RpZnkgZ2xvYmFsIHN0eWxpbmcgYnkgZW5hYmxpbmcgb3IgZGlzYWJsaW5nIG9wdGlvbmFsIGZlYXR1cmVzLlxuXG4kZW5hYmxlLWNhcmV0OiB0cnVlO1xuJGVuYWJsZS1yb3VuZGVkOiB0cnVlO1xuJGVuYWJsZS1zaGFkb3dzOiBmYWxzZTtcbiRlbmFibGUtZ3JhZGllbnRzOiBmYWxzZTtcbiRlbmFibGUtdHJhbnNpdGlvbnM6IHRydWU7XG4kZW5hYmxlLXJlZHVjZWQtbW90aW9uOiB0cnVlO1xuJGVuYWJsZS1zbW9vdGgtc2Nyb2xsOiB0cnVlO1xuJGVuYWJsZS1ncmlkLWNsYXNzZXM6IHRydWU7XG4kZW5hYmxlLWNzc2dyaWQ6IGZhbHNlO1xuJGVuYWJsZS1idXR0b24tcG9pbnRlcnM6IHRydWU7XG4kZW5hYmxlLXJmczogdHJ1ZTtcbiRlbmFibGUtdmFsaWRhdGlvbi1pY29uczogdHJ1ZTtcbiRlbmFibGUtbmVnYXRpdmUtbWFyZ2luczogdHJ1ZTtcbiRlbmFibGUtZGVwcmVjYXRpb24tbWVzc2FnZXM6IHRydWU7XG4kZW5hYmxlLWltcG9ydGFudC11dGlsaXRpZXM6IHRydWU7XG5cbi8vIFByZWZpeCBmb3IgOnJvb3QgQ1NTIHZhcmlhYmxlc1xuXG4kdmFyaWFibGUtcHJlZml4OiBicy07XG5cbi8vIEdyYWRpZW50XG4vL1xuLy8gVGhlIGdyYWRpZW50IHdoaWNoIGlzIGFkZGVkIHRvIGNvbXBvbmVudHMgaWYgYCRlbmFibGUtZ3JhZGllbnRzYCBpcyBgdHJ1ZWBcbi8vIFRoaXMgZ3JhZGllbnQgaXMgYWxzbyBhZGRlZCB0byBlbGVtZW50cyB3aXRoIGAuYmctZ3JhZGllbnRgXG4kZ3JhZGllbnQ6IGxpbmVhci1ncmFkaWVudCgxODBkZWcsIHJnYmEoJHdoaXRlLCAwLjE1KSwgcmdiYSgkd2hpdGUsIDApKTtcblxuLy8gU3BhY2luZ1xuLy9cbi8vIENvbnRyb2wgdGhlIGRlZmF1bHQgc3R5bGluZyBvZiBtb3N0IEJvb3RzdHJhcCBlbGVtZW50cyBieSBtb2RpZnlpbmcgdGhlc2Vcbi8vIHZhcmlhYmxlcy4gTW9zdGx5IGZvY3VzZWQgb24gc3BhY2luZy5cbi8vIFlvdSBjYW4gYWRkIG1vcmUgZW50cmllcyB0byB0aGUgJHNwYWNlcnMgbWFwLCBzaG91bGQgeW91IG5lZWQgbW9yZSB2YXJpYXRpb24uXG5cbiRzcGFjZXI6IDFyZW07XG4kc3BhY2VyczogKFxuICAwOiAwLFxuICAxOiAkc3BhY2VyICogMC4yNSxcbiAgMjogJHNwYWNlciAqIDAuNSxcbiAgMzogJHNwYWNlcixcbiAgNDogJHNwYWNlciAqIDEuNSxcbiAgNTogJHNwYWNlciAqIDMsXG4pO1xuXG4kbmVnYXRpdmUtc3BhY2VyczogaWYoJGVuYWJsZS1uZWdhdGl2ZS1tYXJnaW5zLCBuZWdhdGl2aWZ5LW1hcCgkc3BhY2VycyksIG51bGwpO1xuXG4vLyBQb3NpdGlvblxuLy9cbi8vIERlZmluZSB0aGUgZWRnZSBwb3NpdGlvbmluZyBhbmNob3JzIG9mIHRoZSBwb3NpdGlvbiB1dGlsaXRpZXMuXG5cbiRwb3NpdGlvbi12YWx1ZXM6IChcbiAgMDogMCxcbiAgNTA6IDUwJSxcbiAgMTAwOiAxMDAlLFxuKTtcblxuLy8gQm9keVxuLy9cbi8vIFNldHRpbmdzIGZvciB0aGUgYDxib2R5PmAgZWxlbWVudC5cbiRib2R5LWJnOiAjZjhmOWZkOyAvLyBjaGFuZ2VcbiRib2R5LWNvbG9yOiAjMjkzMjQwOyAvLyBjaGFuZ2VcbiRib2R5LXRleHQtYWxpZ246IG51bGw7XG5cbi8vIFV0aWxpdGllcyBtYXBzXG4vL1xuLy8gRXh0ZW5kcyB0aGUgZGVmYXVsdCBgJHRoZW1lLWNvbG9yc2AgbWFwcyB0byBoZWxwIGNyZWF0ZSBvdXIgdXRpbGl0aWVzLlxuXG4vLyBzY3NzLWRvY3Mtc3RhcnQgdXRpbGl0aWVzLWNvbG9yc1xuJHV0aWxpdGllcy1jb2xvcnM6IG1hcC1tZXJnZShcbiAgJHRoZW1lLWNvbG9ycy1yZ2IsXG4gIChcbiAgICBcImJsYWNrXCI6IHRvLXJnYigkYmxhY2spLFxuICAgIFwid2hpdGVcIjogdG8tcmdiKCR3aGl0ZSksXG4gICAgXCJib2R5XCI6IHRvLXJnYigkYm9keS1jb2xvciksXG4gIClcbik7XG4vLyBzY3NzLWRvY3MtZW5kIHV0aWxpdGllcy1jb2xvcnNcblxuLy8gc2Nzcy1kb2NzLXN0YXJ0IHV0aWxpdGllcy10ZXh0LWNvbG9yc1xuJHV0aWxpdGllcy10ZXh0LWNvbG9yczogbWFwLWxvb3AoXG4gICR1dGlsaXRpZXMtY29sb3JzLFxuICByZ2JhLWNzcy12YXIsXG4gIFwiJGtleVwiLFxuICBcInRleHRcIlxuKTtcbi8vIHNjc3MtZG9jcy1lbmQgdXRpbGl0aWVzLXRleHQtY29sb3JzXG5cbi8vIHNjc3MtZG9jcy1zdGFydCB1dGlsaXRpZXMtYmctY29sb3JzXG4kdXRpbGl0aWVzLWJnLWNvbG9yczogbWFwLWxvb3AoJHV0aWxpdGllcy1jb2xvcnMsIHJnYmEtY3NzLXZhciwgXCIka2V5XCIsIFwiYmdcIik7XG4vLyBzY3NzLWRvY3MtZW5kIHV0aWxpdGllcy1iZy1jb2xvcnNcblxuLy8gTGlua3Ncbi8vXG4vLyBTdHlsZSBhbmNob3IgZWxlbWVudHMuXG5cbiRsaW5rLWNvbG9yOiAkcHJpbWFyeTtcbiRsaW5rLWRlY29yYXRpb246IG5vbmU7XG4kbGluay1zaGFkZS1wZXJjZW50YWdlOiAyMCU7XG4kbGluay1ob3Zlci1jb2xvcjogJGxpbmstY29sb3I7IC8vIGNoYW5nZVxuJGxpbmstaG92ZXItZGVjb3JhdGlvbjogdW5kZXJsaW5lO1xuXG4kc3RyZXRjaGVkLWxpbmstcHNldWRvLWVsZW1lbnQ6IGFmdGVyO1xuJHN0cmV0Y2hlZC1saW5rLXotaW5kZXg6IDE7XG5cbi8vIFBhcmFncmFwaHNcbi8vXG4vLyBTdHlsZSBwIGVsZW1lbnQuXG5cbiRwYXJhZ3JhcGgtbWFyZ2luLWJvdHRvbTogMXJlbTtcblxuLy8gR3JpZCBicmVha3BvaW50c1xuLy9cbi8vIERlZmluZSB0aGUgbWluaW11bSBkaW1lbnNpb25zIGF0IHdoaWNoIHlvdXIgbGF5b3V0IHdpbGwgY2hhbmdlLFxuLy8gYWRhcHRpbmcgdG8gZGlmZmVyZW50IHNjcmVlbiBzaXplcywgZm9yIHVzZSBpbiBtZWRpYSBxdWVyaWVzLlxuXG4vLyBzY3NzLWRvY3Mtc3RhcnQgZ3JpZC1icmVha3BvaW50c1xuJGdyaWQtYnJlYWtwb2ludHM6IChcbiAgeHM6IDAsXG4gIHNtOiA1NzZweCxcbiAgbWQ6IDc2OHB4LFxuICBsZzogOTkycHgsXG4gIHhsOiAxMjAwcHgsXG4gIHh4bDogMTQwMHB4LFxuKTtcbi8vIHNjc3MtZG9jcy1lbmQgZ3JpZC1icmVha3BvaW50c1xuXG5AaW5jbHVkZSBfYXNzZXJ0LWFzY2VuZGluZygkZ3JpZC1icmVha3BvaW50cywgXCIkZ3JpZC1icmVha3BvaW50c1wiKTtcbkBpbmNsdWRlIF9hc3NlcnQtc3RhcnRzLWF0LXplcm8oJGdyaWQtYnJlYWtwb2ludHMsIFwiJGdyaWQtYnJlYWtwb2ludHNcIik7XG5cbi8vIEdyaWQgY29udGFpbmVyc1xuLy9cbi8vIERlZmluZSB0aGUgbWF4aW11bSB3aWR0aCBvZiBgLmNvbnRhaW5lcmAgZm9yIGRpZmZlcmVudCBzY3JlZW4gc2l6ZXMuXG5cbi8vIHNjc3MtZG9jcy1zdGFydCBjb250YWluZXItbWF4LXdpZHRoc1xuJGNvbnRhaW5lci1tYXgtd2lkdGhzOiAoXG4gIHNtOiA1NDBweCxcbiAgbWQ6IDcyMHB4LFxuICBsZzogOTYwcHgsXG4gIHhsOiAxMTQwcHgsXG4gIHh4bDogMTMyMHB4LFxuKTtcbi8vIHNjc3MtZG9jcy1lbmQgY29udGFpbmVyLW1heC13aWR0aHNcblxuQGluY2x1ZGUgX2Fzc2VydC1hc2NlbmRpbmcoJGNvbnRhaW5lci1tYXgtd2lkdGhzLCBcIiRjb250YWluZXItbWF4LXdpZHRoc1wiKTtcblxuLy8gR3JpZCBjb2x1bW5zXG4vL1xuLy8gU2V0IHRoZSBudW1iZXIgb2YgY29sdW1ucyBhbmQgc3BlY2lmeSB0aGUgd2lkdGggb2YgdGhlIGd1dHRlcnMuXG5cbiRncmlkLWNvbHVtbnM6IDEyO1xuJGdyaWQtZ3V0dGVyLXdpZHRoOiAxLjVyZW07XG4kZ3JpZC1yb3ctY29sdW1uczogNjtcblxuJGd1dHRlcnM6ICRzcGFjZXJzO1xuXG4vLyBDb250YWluZXIgcGFkZGluZ1xuXG4kY29udGFpbmVyLXBhZGRpbmcteDogJGdyaWQtZ3V0dGVyLXdpZHRoO1xuXG4vLyBDb21wb25lbnRzXG4vL1xuLy8gRGVmaW5lIGNvbW1vbiBwYWRkaW5nIGFuZCBib3JkZXIgcmFkaXVzIHNpemVzIGFuZCBtb3JlLlxuXG4vLyBzY3NzLWRvY3Mtc3RhcnQgYm9yZGVyLXZhcmlhYmxlc1xuJGJvcmRlci13aWR0aDogMXB4O1xuJGJvcmRlci13aWR0aHM6IChcbiAgMDogMCxcbiAgMTogMXB4LFxuICAyOiAycHgsXG4gIDM6IDNweCxcbiAgNDogNHB4LFxuICA1OiA1cHgsXG4pO1xuXG4kYm9yZGVyLWNvbG9yOiAjZjFmMWYxO1xuXG4kYm9yZGVyLXJhZGl1czogMTBweDsgLy8gY2hhbmdlXG4kYm9yZGVyLXJhZGl1cy1sZzogMTVweDsgLy8gY2hhbmdlXG4kYm9yZGVyLXJhZGl1cy1zbTogOHB4OyAvLyBjaGFuZ2VcblxuJGJvcmRlci1yYWRpdXMtcGlsbDogNTByZW07XG4vLyBzY3NzLWRvY3MtZW5kIGJvcmRlci1yYWRpdXMtdmFyaWFibGVzXG5cbiRib3gtc2hhZG93LXNtOiAwIDAuMTI1cmVtIDAuMjVyZW0gcmdiYSgkYmxhY2ssIDAuMDc1KTtcbiRib3gtc2hhZG93OiAwIDAuNXJlbSAxcmVtIHJnYmEoJGJsYWNrLCAwLjE1KTtcbiRib3gtc2hhZG93LWxnOiAwIDFyZW0gM3JlbSByZ2JhKCRibGFjaywgMC4xNzUpO1xuJGJveC1zaGFkb3ctaW5zZXQ6IGluc2V0IDAgMXB4IDJweCByZ2JhKCRibGFjaywgMC4wNzUpO1xuXG4kY29tcG9uZW50LWFjdGl2ZS1jb2xvcjogJHdoaXRlO1xuJGNvbXBvbmVudC1hY3RpdmUtYmc6ICRwcmltYXJ5O1xuXG4vLyBzY3NzLWRvY3Mtc3RhcnQgY2FyZXQtdmFyaWFibGVzXG4kY2FyZXQtd2lkdGg6IDAuM2VtO1xuJGNhcmV0LXZlcnRpY2FsLWFsaWduOiAkY2FyZXQtd2lkdGggKiAwLjg1O1xuJGNhcmV0LXNwYWNpbmc6ICRjYXJldC13aWR0aCAqIDAuODU7XG4vLyBzY3NzLWRvY3MtZW5kIGNhcmV0LXZhcmlhYmxlc1xuXG4kdHJhbnNpdGlvbi1iYXNlOiBhbGwgMC4ycyBlYXNlLWluLW91dDtcbiR0cmFuc2l0aW9uLWZhZGU6IG9wYWNpdHkgMC4xNXMgbGluZWFyO1xuLy8gc2Nzcy1kb2NzLXN0YXJ0IGNvbGxhcHNlLXRyYW5zaXRpb25cbiR0cmFuc2l0aW9uLWNvbGxhcHNlOiBoZWlnaHQgMC4zNXMgZWFzZTtcbiR0cmFuc2l0aW9uLWNvbGxhcHNlLXdpZHRoOiB3aWR0aCAwLjM1cyBlYXNlO1xuXG4vLyBzdHlsZWxpbnQtZGlzYWJsZSBmdW5jdGlvbi1kaXNhbGxvd2VkLWxpc3Rcbi8vIHNjc3MtZG9jcy1zdGFydCBhc3BlY3QtcmF0aW9zXG4kYXNwZWN0LXJhdGlvczogKFxuICBcIjF4MVwiOiAxMDAlLFxuICBcIjR4M1wiOiBjYWxjKDMgLyA0ICogMTAwJSksXG4gIFwiMTZ4OVwiOiBjYWxjKDkgLyAxNiAqIDEwMCUpLFxuICBcIjIxeDlcIjogY2FsYyg5IC8gMjEgKiAxMDAlKSxcbik7XG4vLyBzY3NzLWRvY3MtZW5kIGFzcGVjdC1yYXRpb3Ncbi8vIHN0eWxlbGludC1lbmFibGUgZnVuY3Rpb24tZGlzYWxsb3dlZC1saXN0XG5cbi8vIFR5cG9ncmFwaHlcbi8vXG4vLyBGb250LCBsaW5lLWhlaWdodCwgYW5kIGNvbG9yIGZvciBib2R5IHRleHQsIGhlYWRpbmdzLCBhbmQgbW9yZS5cblxuLy8gc3R5bGVsaW50LWRpc2FibGUgdmFsdWUta2V5d29yZC1jYXNlXG4kZm9udC1mYW1pbHktc2Fucy1zZXJpZjogXCJPcGVuIFNhbnNcIiwgc2Fucy1zZXJpZjsgLy8gY2hhbmdlXG4kZm9udC1mYW1pbHktbW9ub3NwYWNlOiBTRk1vbm8tUmVndWxhciwgTWVubG8sIE1vbmFjbywgQ29uc29sYXMsXG4gIFwiTGliZXJhdGlvbiBNb25vXCIsIFwiQ291cmllciBOZXdcIiwgbW9ub3NwYWNlO1xuLy8gc3R5bGVsaW50LWVuYWJsZSB2YWx1ZS1rZXl3b3JkLWNhc2VcbiRmb250LWZhbWlseS1iYXNlOiB2YXIoLS0jeyR2YXJpYWJsZS1wcmVmaXh9Zm9udC1zYW5zLXNlcmlmKTtcbiRmb250LWZhbWlseS1jb2RlOiB2YXIoLS0jeyR2YXJpYWJsZS1wcmVmaXh9Zm9udC1tb25vc3BhY2UpO1xuXG4kZm9udC1zaXplLXJvb3Q6IG51bGw7XG4kZm9udC1zaXplLWJhc2U6IDAuODc1cmVtO1xuJGZvbnQtc2l6ZS1zbTogJGZvbnQtc2l6ZS1iYXNlICogMC44NzU7XG4kZm9udC1zaXplLWxnOiAkZm9udC1zaXplLWJhc2UgKiAxLjI1O1xuJGZvbnQtd2VpZ2h0LWxpZ2h0ZXI6IGxpZ2h0ZXI7XG4kZm9udC13ZWlnaHQtbGlnaHQ6IDMwMDtcbiRmb250LXdlaWdodC1ub3JtYWw6IDQwMDtcbiRmb250LXdlaWdodC1ib2xkOiA3MDA7XG4kZm9udC13ZWlnaHQtYm9sZGVyOiBib2xkZXI7XG5cbiRmb250LXdlaWdodC1iYXNlOiAkZm9udC13ZWlnaHQtbm9ybWFsO1xuJGxpbmUtaGVpZ2h0LWJhc2U6IDEuNTtcbiRsaW5lLWhlaWdodC1zbTogMS4yNTtcbiRsaW5lLWhlaWdodC1sZzogMjtcblxuJGgxLWZvbnQtc2l6ZTogMzZweDsgLy8gY2hhbmdlXG4kaDItZm9udC1zaXplOiAyOHB4OyAvLyBjaGFuZ2VcbiRoMy1mb250LXNpemU6IDI0cHg7IC8vIGNoYW5nZVxuJGg0LWZvbnQtc2l6ZTogMjBweDsgLy8gY2hhbmdlXG4kaDUtZm9udC1zaXplOiAxNnB4OyAvLyBjaGFuZ2VcbiRoNi1mb250LXNpemU6IDE1cHg7IC8vIGNoYW5nZVxuXG4vLyBzY3NzLWRvY3Mtc3RhcnQgZm9udC1zaXplc1xuJGZvbnQtc2l6ZXM6IChcbiAgMTogJGgxLWZvbnQtc2l6ZSxcbiAgMjogJGgyLWZvbnQtc2l6ZSxcbiAgMzogJGgzLWZvbnQtc2l6ZSxcbiAgNDogJGg0LWZvbnQtc2l6ZSxcbiAgNTogJGg1LWZvbnQtc2l6ZSxcbiAgNjogJGg2LWZvbnQtc2l6ZSxcbik7XG4vLyBzY3NzLWRvY3MtZW5kIGZvbnQtc2l6ZXNcblxuJGhlYWRpbmdzLW1hcmdpbi1ib3R0b206ICRzcGFjZXIgLyAyO1xuJGhlYWRpbmdzLWZvbnQtZmFtaWx5OiBudWxsO1xuJGhlYWRpbmdzLWZvbnQtc3R5bGU6IG51bGw7XG4kaGVhZGluZ3MtZm9udC13ZWlnaHQ6IDYwMCAjey8qIHJ0bDo0MDAgKi99O1xuJGhlYWRpbmdzLWxpbmUtaGVpZ2h0OiAxLjI7XG4kaGVhZGluZ3MtY29sb3I6ICMwNjA2MDY7XG5cbi8vIHNjc3MtZG9jcy1zdGFydCBkaXNwbGF5LWhlYWRpbmdzXG4kZGlzcGxheS1mb250LXNpemVzOiAoXG4gIDE6IDVyZW0sXG4gIDI6IDQuNXJlbSxcbiAgMzogNHJlbSxcbiAgNDogMy41cmVtLFxuICA1OiAzcmVtLFxuICA2OiAyLjVyZW0sXG4pO1xuXG4kZGlzcGxheS1mb250LXdlaWdodDogMzAwO1xuJGRpc3BsYXktbGluZS1oZWlnaHQ6ICRoZWFkaW5ncy1saW5lLWhlaWdodDtcbi8vIHNjc3MtZG9jcy1lbmQgZGlzcGxheS1oZWFkaW5nc1xuXG4kbGVhZC1mb250LXNpemU6ICRmb250LXNpemUtYmFzZSAqIDEuMjU7XG4kbGVhZC1mb250LXdlaWdodDogMzAwO1xuXG4kc21hbGwtZm9udC1zaXplOiA4MCU7XG5cbiRzdWItc3VwLWZvbnQtc2l6ZTogMC43NWVtO1xuXG4kdGV4dC1tdXRlZDogJGdyYXktNjAwO1xuXG4kaW5pdGlhbGlzbS1mb250LXNpemU6ICRzbWFsbC1mb250LXNpemU7XG4kYmxvY2txdW90ZS1tYXJnaW4teTogJHNwYWNlcjtcbiRibG9ja3F1b3RlLWZvbnQtc2l6ZTogJGZvbnQtc2l6ZS1iYXNlICogMS4yNTtcbiRibG9ja3F1b3RlLWZvb3Rlci1jb2xvcjogJGdyYXktNjAwO1xuJGJsb2NrcXVvdGUtZm9vdGVyLWZvbnQtc2l6ZTogJHNtYWxsLWZvbnQtc2l6ZTtcblxuJGhyLW1hcmdpbi15OiAkc3BhY2VyO1xuJGhyLWNvbG9yOiAkYm9yZGVyLWNvbG9yO1xuJGhyLWhlaWdodDogJGJvcmRlci13aWR0aDtcbiRoci1vcGFjaXR5OiAxO1xuXG4kbGVnZW5kLW1hcmdpbi1ib3R0b206IDAuNXJlbTtcbiRsZWdlbmQtZm9udC1zaXplOiAxLjVyZW07XG4kbGVnZW5kLWZvbnQtd2VpZ2h0OiBudWxsO1xuXG4kbWFyay1wYWRkaW5nOiAwLjJlbTtcblxuJGR0LWZvbnQtd2VpZ2h0OiAkZm9udC13ZWlnaHQtYm9sZDtcblxuJG5lc3RlZC1rYmQtZm9udC13ZWlnaHQ6ICRmb250LXdlaWdodC1ib2xkO1xuXG4kbGlzdC1pbmxpbmUtcGFkZGluZzogMC41cmVtO1xuXG4kbWFyay1iZzogI2ZjZjhlMztcbi8vIFRhYmxlc1xuLy9cbi8vIEN1c3RvbWl6ZXMgdGhlIGAudGFibGVgIGNvbXBvbmVudCB3aXRoIGJhc2ljIHZhbHVlcywgZWFjaCB1c2VkIGFjcm9zcyBhbGwgdGFibGUgdmFyaWF0aW9ucy5cbi8vIHNjc3MtZG9jcy1zdGFydCB0YWJsZS12YXJpYWJsZXNcbiR0YWJsZS1jZWxsLXBhZGRpbmcteTogMC45cmVtO1xuJHRhYmxlLWNlbGwtcGFkZGluZy14OiAwLjc1cmVtO1xuJHRhYmxlLWNlbGwtcGFkZGluZy15LXNtOiAwLjNyZW07XG4kdGFibGUtY2VsbC1wYWRkaW5nLXgtc206IDAuM3JlbTtcblxuJHRhYmxlLWNlbGwtdmVydGljYWwtYWxpZ246IHRvcDtcblxuJHRhYmxlLWNvbG9yOiAkYm9keS1jb2xvcjtcbiR0YWJsZS1iZzogdHJhbnNwYXJlbnQ7XG4kdGFibGUtYWNjZW50LWJnOiB0cmFuc3BhcmVudDtcblxuJHRhYmxlLXRoLWZvbnQtd2VpZ2h0OiBudWxsO1xuJHRhYmxlLXN0cmlwZWQtY29sb3I6ICR0YWJsZS1jb2xvcjtcbiR0YWJsZS1zdHJpcGVkLWJnLWZhY3RvcjogMC4wNTtcbiR0YWJsZS1zdHJpcGVkLWJnOiByZ2JhKCRibGFjaywgJHRhYmxlLXN0cmlwZWQtYmctZmFjdG9yKTtcblxuJHRhYmxlLWFjdGl2ZS1jb2xvcjogJHRhYmxlLWNvbG9yO1xuJHRhYmxlLWFjdGl2ZS1iZy1mYWN0b3I6IDAuMTtcbiR0YWJsZS1hY3RpdmUtYmc6IHJnYmEoJGJsYWNrLCAkdGFibGUtYWN0aXZlLWJnLWZhY3Rvcik7XG5cbiR0YWJsZS1ob3Zlci1jb2xvcjogJHRhYmxlLWNvbG9yO1xuJHRhYmxlLWhvdmVyLWJnLWZhY3RvcjogMC4wMjtcbiR0YWJsZS1ob3Zlci1iZzogcmdiYSgkcHJpbWFyeSwgJHRhYmxlLWhvdmVyLWJnLWZhY3Rvcik7XG5cbiR0YWJsZS1ib3JkZXItZmFjdG9yOiAwLjE7XG4kdGFibGUtYm9yZGVyLXdpZHRoOiAkYm9yZGVyLXdpZHRoO1xuJHRhYmxlLWJvcmRlci1jb2xvcjogJGJvcmRlci1jb2xvcjtcblxuJHRhYmxlLXN0cmlwZWQtb3JkZXI6IG9kZDtcblxuJHRhYmxlLWdyb3VwLXNlcGVyYXRvci1jb2xvcjogY3VycmVudENvbG9yO1xuJHRhYmxlLWNhcHRpb24tY29sb3I6ICR0ZXh0LW11dGVkO1xuXG4kdGFibGUtYmctc2NhbGU6IC04MCU7XG5cbiR0YWJsZS12YXJpYW50czogKFxuICBcInByaW1hcnlcIjogc2hpZnQtY29sb3IoJHByaW1hcnksICR0YWJsZS1iZy1zY2FsZSksXG4gIFwic2Vjb25kYXJ5XCI6IHNoaWZ0LWNvbG9yKCRzZWNvbmRhcnksICR0YWJsZS1iZy1zY2FsZSksXG4gIFwic3VjY2Vzc1wiOiBzaGlmdC1jb2xvcigkc3VjY2VzcywgJHRhYmxlLWJnLXNjYWxlKSxcbiAgXCJpbmZvXCI6IHNoaWZ0LWNvbG9yKCRpbmZvLCAkdGFibGUtYmctc2NhbGUpLFxuICBcIndhcm5pbmdcIjogc2hpZnQtY29sb3IoJHdhcm5pbmcsICR0YWJsZS1iZy1zY2FsZSksXG4gIFwiZGFuZ2VyXCI6IHNoaWZ0LWNvbG9yKCRkYW5nZXIsICR0YWJsZS1iZy1zY2FsZSksXG4gIFwibGlnaHRcIjogJGxpZ2h0LFxuICBcImRhcmtcIjogJGRhcmssXG4pO1xuLy8gc2Nzcy1kb2NzLWVuZCB0YWJsZS12YXJpYWJsZXNcblxuLy8gQnV0dG9ucyArIEZvcm1zXG4vL1xuLy8gU2hhcmVkIHZhcmlhYmxlcyB0aGF0IGFyZSByZWFzc2lnbmVkIHRvIGAkaW5wdXQtYCBhbmQgYCRidG4tYCBzcGVjaWZpYyB2YXJpYWJsZXMuXG5cbiRpbnB1dC1idG4tcGFkZGluZy15OiAwLjU3NXJlbTtcbiRpbnB1dC1idG4tcGFkZGluZy14OiAxLjNyZW07XG4kaW5wdXQtYnRuLWZvbnQtZmFtaWx5OiBudWxsO1xuJGlucHV0LWJ0bi1mb250LXNpemU6IDAuODc1cmVtO1xuJGlucHV0LWJ0bi1saW5lLWhlaWdodDogJGxpbmUtaGVpZ2h0LWJhc2U7XG5cbiRpbnB1dC1idG4tZm9jdXMtd2lkdGg6IDAuMnJlbTtcbiRpbnB1dC1idG4tZm9jdXMtY29sb3Itb3BhY2l0eTogMC4yNTtcbiRpbnB1dC1idG4tZm9jdXMtY29sb3I6IHJnYmEoXG4gICRjb21wb25lbnQtYWN0aXZlLWJnLFxuICAkaW5wdXQtYnRuLWZvY3VzLWNvbG9yLW9wYWNpdHlcbik7XG4kaW5wdXQtYnRuLWZvY3VzLWJsdXI6IDA7XG4kaW5wdXQtYnRuLWZvY3VzLWJveC1zaGFkb3c6IDAgMCAwICRpbnB1dC1idG4tZm9jdXMtd2lkdGggJGlucHV0LWJ0bi1mb2N1cy1jb2xvcjtcblxuJGlucHV0LWJ0bi1wYWRkaW5nLXktc206IDAuMjVyZW07XG4kaW5wdXQtYnRuLXBhZGRpbmcteC1zbTogMC41cmVtO1xuJGlucHV0LWJ0bi1mb250LXNpemUtc206ICRmb250LXNpemUtc207XG5cbiRpbnB1dC1idG4tcGFkZGluZy15LWxnOiAwLjVyZW07XG4kaW5wdXQtYnRuLXBhZGRpbmcteC1sZzogMXJlbTtcbiRpbnB1dC1idG4tZm9udC1zaXplLWxnOiAkZm9udC1zaXplLWxnO1xuXG4kaW5wdXQtYnRuLWJvcmRlci13aWR0aDogMXB4O1xuXG4vLyBCdXR0b25zXG4vL1xuLy8gRm9yIGVhY2ggb2YgQm9vdHN0cmFwJ3MgYnV0dG9ucywgZGVmaW5lIHRleHQsIGJhY2tncm91bmQsIGFuZCBib3JkZXIgY29sb3IuXG5cbiRidG4tcGFkZGluZy15OiAkaW5wdXQtYnRuLXBhZGRpbmcteTtcbiRidG4tcGFkZGluZy14OiAkaW5wdXQtYnRuLXBhZGRpbmcteDtcbiRidG4tZm9udC1mYW1pbHk6ICRpbnB1dC1idG4tZm9udC1mYW1pbHk7XG4kYnRuLWZvbnQtc2l6ZTogJGlucHV0LWJ0bi1mb250LXNpemU7XG4kYnRuLWxpbmUtaGVpZ2h0OiAkaW5wdXQtYnRuLWxpbmUtaGVpZ2h0O1xuJGJ0bi13aGl0ZS1zcGFjZTogbnVsbDsgLy8gU2V0IHRvIGBub3dyYXBgIHRvIHByZXZlbnQgdGV4dCB3cmFwcGluZ1xuXG4kYnRuLXBhZGRpbmcteS1zbTogJGlucHV0LWJ0bi1wYWRkaW5nLXktc207XG4kYnRuLXBhZGRpbmcteC1zbTogJGlucHV0LWJ0bi1wYWRkaW5nLXgtc207XG4kYnRuLWZvbnQtc2l6ZS1zbTogJGlucHV0LWJ0bi1mb250LXNpemUtc207XG5cbiRidG4tcGFkZGluZy15LWxnOiAkaW5wdXQtYnRuLXBhZGRpbmcteS1sZztcbiRidG4tcGFkZGluZy14LWxnOiAkaW5wdXQtYnRuLXBhZGRpbmcteC1sZztcbiRidG4tZm9udC1zaXplLWxnOiAkaW5wdXQtYnRuLWZvbnQtc2l6ZS1sZztcblxuJGJ0bi1ib3JkZXItd2lkdGg6ICRpbnB1dC1idG4tYm9yZGVyLXdpZHRoO1xuXG4kYnRuLWZvbnQtd2VpZ2h0OiA1MDA7XG4kYnRuLWJveC1zaGFkb3c6IGluc2V0IDAgMXB4IDAgcmdiYSgkd2hpdGUsIDAuMTUpLCAwIDFweCAxcHggcmdiYSgkYmxhY2ssIDAuMDc1KTtcbiRidG4tZm9jdXMtd2lkdGg6ICRpbnB1dC1idG4tZm9jdXMtd2lkdGg7XG4kYnRuLWZvY3VzLWJveC1zaGFkb3c6ICRpbnB1dC1idG4tZm9jdXMtYm94LXNoYWRvdztcbiRidG4tZGlzYWJsZWQtb3BhY2l0eTogMC42NTtcbiRidG4tYWN0aXZlLWJveC1zaGFkb3c6IGluc2V0IDAgM3B4IDVweCByZ2JhKCRibGFjaywgMC4xMjUpO1xuXG4kYnRuLWxpbmstY29sb3I6ICRsaW5rLWNvbG9yO1xuJGJ0bi1saW5rLWhvdmVyLWNvbG9yOiAkbGluay1ob3Zlci1jb2xvcjtcbiRidG4tbGluay1kaXNhYmxlZC1jb2xvcjogJGdyYXktNjAwO1xuXG4vLyBBbGxvd3MgZm9yIGN1c3RvbWl6aW5nIGJ1dHRvbiByYWRpdXMgaW5kZXBlbmRlbnRseSBmcm9tIGdsb2JhbCBib3JkZXIgcmFkaXVzXG5cbiRidG4tYm9yZGVyLXJhZGl1czogNnB4O1xuJGJ0bi1ib3JkZXItcmFkaXVzLXNtOiA0cHg7XG4kYnRuLWJvcmRlci1yYWRpdXMtbGc6IDhweDtcblxuJGJ0bi10cmFuc2l0aW9uOiBjb2xvciAwLjE1cyBlYXNlLWluLW91dCwgYmFja2dyb3VuZC1jb2xvciAwLjE1cyBlYXNlLWluLW91dCxcbiAgYm9yZGVyLWNvbG9yIDAuMTVzIGVhc2UtaW4tb3V0LCBib3gtc2hhZG93IDAuMTVzIGVhc2UtaW4tb3V0O1xuXG4kYnRuLWhvdmVyLWJnLXNoYWRlLWFtb3VudDogMTUlO1xuJGJ0bi1ob3Zlci1iZy10aW50LWFtb3VudDogMTUlO1xuJGJ0bi1ob3Zlci1ib3JkZXItc2hhZGUtYW1vdW50OiAyMCU7XG4kYnRuLWhvdmVyLWJvcmRlci10aW50LWFtb3VudDogMTAlO1xuJGJ0bi1hY3RpdmUtYmctc2hhZGUtYW1vdW50OiAyMCU7XG4kYnRuLWFjdGl2ZS1iZy10aW50LWFtb3VudDogMjAlO1xuJGJ0bi1hY3RpdmUtYm9yZGVyLXNoYWRlLWFtb3VudDogMjUlO1xuJGJ0bi1hY3RpdmUtYm9yZGVyLXRpbnQtYW1vdW50OiAxMCU7XG4vLyBzY3NzLWRvY3MtZW5kIGJ0bi12YXJpYWJsZXNcblxuLy8gRm9ybXNcblxuJGZvcm0tdGV4dC1tYXJnaW4tdG9wOiAwLjI1cmVtO1xuJGZvcm0tdGV4dC1mb250LXNpemU6ICRzbWFsbC1mb250LXNpemU7XG4kZm9ybS10ZXh0LWZvbnQtc3R5bGU6IG51bGw7XG4kZm9ybS10ZXh0LWZvbnQtd2VpZ2h0OiBudWxsO1xuJGZvcm0tdGV4dC1jb2xvcjogJHRleHQtbXV0ZWQ7XG5cbiRmb3JtLWxhYmVsLW1hcmdpbi1ib3R0b206IDAuNXJlbTtcbiRmb3JtLWxhYmVsLWZvbnQtc2l6ZTogbnVsbDtcbiRmb3JtLWxhYmVsLWZvbnQtc3R5bGU6IG51bGw7XG4kZm9ybS1sYWJlbC1mb250LXdlaWdodDogbnVsbDtcbiRmb3JtLWxhYmVsLWNvbG9yOiBudWxsO1xuXG4kaW5wdXQtcGFkZGluZy15OiAkaW5wdXQtYnRuLXBhZGRpbmcteTtcbiRpbnB1dC1wYWRkaW5nLXg6IDFyZW07XG4kaW5wdXQtZm9udC1mYW1pbHk6ICRpbnB1dC1idG4tZm9udC1mYW1pbHk7XG4kaW5wdXQtZm9udC1zaXplOiAkaW5wdXQtYnRuLWZvbnQtc2l6ZTtcbiRpbnB1dC1mb250LXdlaWdodDogJGZvbnQtd2VpZ2h0LWJhc2U7XG4kaW5wdXQtbGluZS1oZWlnaHQ6ICRpbnB1dC1idG4tbGluZS1oZWlnaHQ7XG5cbiRpbnB1dC1wYWRkaW5nLXktc206ICRpbnB1dC1idG4tcGFkZGluZy15LXNtO1xuJGlucHV0LXBhZGRpbmcteC1zbTogJGlucHV0LWJ0bi1wYWRkaW5nLXgtc207XG4kaW5wdXQtZm9udC1zaXplLXNtOiAkaW5wdXQtYnRuLWZvbnQtc2l6ZS1zbTtcblxuJGlucHV0LXBhZGRpbmcteS1sZzogJGlucHV0LWJ0bi1wYWRkaW5nLXktbGc7XG4kaW5wdXQtcGFkZGluZy14LWxnOiAkaW5wdXQtYnRuLXBhZGRpbmcteC1sZztcbiRpbnB1dC1mb250LXNpemUtbGc6ICRpbnB1dC1idG4tZm9udC1zaXplLWxnO1xuXG4kaW5wdXQtYmc6ICR3aGl0ZTtcbiRpbnB1dC1kaXNhYmxlZC1iZzogJGdyYXktMjAwO1xuJGlucHV0LWRpc2FibGVkLWJvcmRlci1jb2xvcjogbnVsbDtcblxuJGlucHV0LWNvbG9yOiAkYm9keS1jb2xvcjtcbiRpbnB1dC1ib3JkZXItY29sb3I6ICRncmF5LTQwMDtcbiRpbnB1dC1ib3JkZXItd2lkdGg6ICRpbnB1dC1idG4tYm9yZGVyLXdpZHRoO1xuJGlucHV0LWJveC1zaGFkb3c6IGluc2V0IDAgMXB4IDFweCByZ2JhKCRibGFjaywgMC4wNzUpO1xuXG4kaW5wdXQtYm9yZGVyLXJhZGl1czogNnB4O1xuJGlucHV0LWJvcmRlci1yYWRpdXMtc206IDRweDtcbiRpbnB1dC1ib3JkZXItcmFkaXVzLWxnOiA4cHg7XG5cbiRpbnB1dC1mb2N1cy1iZzogJGlucHV0LWJnO1xuJGlucHV0LWZvY3VzLWJvcmRlci1jb2xvcjogJHByaW1hcnk7XG4kaW5wdXQtZm9jdXMtY29sb3I6ICRpbnB1dC1jb2xvcjtcbiRpbnB1dC1mb2N1cy13aWR0aDogJGlucHV0LWJ0bi1mb2N1cy13aWR0aDtcbiRpbnB1dC1mb2N1cy1ib3gtc2hhZG93OiAkaW5wdXQtYnRuLWZvY3VzLWJveC1zaGFkb3c7XG5cbiRpbnB1dC1wbGFjZWhvbGRlci1jb2xvcjogJGdyYXktNjAwO1xuJGlucHV0LXBsYWludGV4dC1jb2xvcjogJGJvZHktY29sb3I7XG5cbiRpbnB1dC1oZWlnaHQtYm9yZGVyOiAkaW5wdXQtYm9yZGVyLXdpZHRoICogMjtcblxuJGlucHV0LWhlaWdodC1pbm5lcjogYWRkKCRpbnB1dC1saW5lLWhlaWdodCAqIDFlbSwgJGlucHV0LXBhZGRpbmcteSAqIDIpO1xuJGlucHV0LWhlaWdodC1pbm5lci1oYWxmOiBhZGQoJGlucHV0LWxpbmUtaGVpZ2h0ICogMC41ZW0sICRpbnB1dC1wYWRkaW5nLXkpO1xuJGlucHV0LWhlaWdodC1pbm5lci1xdWFydGVyOiBhZGQoXG4gICRpbnB1dC1saW5lLWhlaWdodCAqIDAuMjVlbSxcbiAgJGlucHV0LXBhZGRpbmcteSAvIDJcbik7XG5cbiRpbnB1dC1oZWlnaHQ6IGFkZChcbiAgJGlucHV0LWxpbmUtaGVpZ2h0ICogMWVtLFxuICBhZGQoJGlucHV0LXBhZGRpbmcteSAqIDIsICRpbnB1dC1oZWlnaHQtYm9yZGVyLCBmYWxzZSlcbik7XG4kaW5wdXQtaGVpZ2h0LXNtOiBhZGQoXG4gICRpbnB1dC1saW5lLWhlaWdodCAqIDFlbSxcbiAgYWRkKCRpbnB1dC1wYWRkaW5nLXktc20gKiAyLCAkaW5wdXQtaGVpZ2h0LWJvcmRlciwgZmFsc2UpXG4pO1xuJGlucHV0LWhlaWdodC1sZzogYWRkKFxuICAkaW5wdXQtbGluZS1oZWlnaHQgKiAxZW0sXG4gIGFkZCgkaW5wdXQtcGFkZGluZy15LWxnICogMiwgJGlucHV0LWhlaWdodC1ib3JkZXIsIGZhbHNlKVxuKTtcblxuJGlucHV0LXRyYW5zaXRpb246IGJvcmRlci1jb2xvciAwLjE1cyBlYXNlLWluLW91dCwgYm94LXNoYWRvdyAwLjE1cyBlYXNlLWluLW91dDtcblxuJGZvcm0tY29sb3Itd2lkdGg6IDNyZW07XG4vLyBzY3NzLWRvY3MtZW5kIGZvcm0taW5wdXQtdmFyaWFibGVzXG5cbiRmb3JtLWNoZWNrLWlucHV0LXdpZHRoOiAxLjI1ZW07XG4kZm9ybS1jaGVjay1taW4taGVpZ2h0OiAkZm9udC1zaXplLWJhc2UgKiAkbGluZS1oZWlnaHQtYmFzZTtcbiRmb3JtLWNoZWNrLXBhZGRpbmctc3RhcnQ6ICRmb3JtLWNoZWNrLWlucHV0LXdpZHRoICsgMC41ZW07XG4kZm9ybS1jaGVjay1tYXJnaW4tYm90dG9tOiAwLjEyNXJlbTtcbiRmb3JtLWNoZWNrLWxhYmVsLWNvbG9yOiBudWxsO1xuJGZvcm0tY2hlY2stbGFiZWwtY3Vyc29yOiBudWxsO1xuJGZvcm0tY2hlY2stdHJhbnNpdGlvbjogYmFja2dyb3VuZC1jb2xvciAwLjE1cyBlYXNlLWluLW91dCxcbiAgYmFja2dyb3VuZC1wb3NpdGlvbiAwLjE1cyBlYXNlLWluLW91dCwgYm9yZGVyLWNvbG9yIDAuMTVzIGVhc2UtaW4tb3V0LFxuICBib3gtc2hhZG93IDAuMTVzIGVhc2UtaW4tb3V0O1xuXG4kZm9ybS1jaGVjay1pbnB1dC1hY3RpdmUtZmlsdGVyOiBicmlnaHRuZXNzKDkwJSk7XG5cbiRmb3JtLWNoZWNrLWlucHV0LWJnOiAkaW5wdXQtYmc7XG4kZm9ybS1jaGVjay1pbnB1dC1ib3JkZXI6IDFweCBzb2xpZCByZ2JhKDAsIDAsIDAsIDAuMjUpO1xuJGZvcm0tY2hlY2staW5wdXQtYm9yZGVyLXJhZGl1czogMC4yNWVtO1xuJGZvcm0tY2hlY2stcmFkaW8tYm9yZGVyLXJhZGl1czogNTAlO1xuJGZvcm0tY2hlY2staW5wdXQtZm9jdXMtYm9yZGVyOiAkaW5wdXQtZm9jdXMtYm9yZGVyLWNvbG9yO1xuJGZvcm0tY2hlY2staW5wdXQtZm9jdXMtYm94LXNoYWRvdzogJGlucHV0LWJ0bi1mb2N1cy1ib3gtc2hhZG93O1xuXG4kZm9ybS1jaGVjay1pbnB1dC1jaGVja2VkLWNvbG9yOiAkY29tcG9uZW50LWFjdGl2ZS1jb2xvcjtcbiRmb3JtLWNoZWNrLWlucHV0LWNoZWNrZWQtYmctY29sb3I6ICRjb21wb25lbnQtYWN0aXZlLWJnO1xuJGZvcm0tY2hlY2staW5wdXQtY2hlY2tlZC1ib3JkZXItY29sb3I6ICRmb3JtLWNoZWNrLWlucHV0LWNoZWNrZWQtYmctY29sb3I7XG4kZm9ybS1jaGVjay1pbnB1dC1jaGVja2VkLWJnLWltYWdlOiB1cmwoXCJkYXRhOmltYWdlL3N2Zyt4bWwsPHN2ZyB4bWxucz0naHR0cDovL3d3dy53My5vcmcvMjAwMC9zdmcnIHZpZXdCb3g9JzAgMCAyMCAyMCc+PHBhdGggZmlsbD0nbm9uZScgc3Ryb2tlPScjeyRmb3JtLWNoZWNrLWlucHV0LWNoZWNrZWQtY29sb3J9JyBzdHJva2UtbGluZWNhcD0ncm91bmQnIHN0cm9rZS1saW5lam9pbj0ncm91bmQnIHN0cm9rZS13aWR0aD0nMycgZD0nTTYgMTBsMyAzbDYtNicvPjwvc3ZnPlwiKTtcbiRmb3JtLWNoZWNrLXJhZGlvLWNoZWNrZWQtYmctaW1hZ2U6IHVybChcImRhdGE6aW1hZ2Uvc3ZnK3htbCw8c3ZnIHhtbG5zPSdodHRwOi8vd3d3LnczLm9yZy8yMDAwL3N2Zycgdmlld0JveD0nLTQgLTQgOCA4Jz48Y2lyY2xlIHI9JzInIGZpbGw9JyN7JGZvcm0tY2hlY2staW5wdXQtY2hlY2tlZC1jb2xvcn0nLz48L3N2Zz5cIik7XG5cbiRmb3JtLWNoZWNrLWlucHV0LWluZGV0ZXJtaW5hdGUtY29sb3I6ICRjb21wb25lbnQtYWN0aXZlLWNvbG9yO1xuJGZvcm0tY2hlY2staW5wdXQtaW5kZXRlcm1pbmF0ZS1iZy1jb2xvcjogJGNvbXBvbmVudC1hY3RpdmUtYmc7XG4kZm9ybS1jaGVjay1pbnB1dC1pbmRldGVybWluYXRlLWJvcmRlci1jb2xvcjogJGZvcm0tY2hlY2staW5wdXQtaW5kZXRlcm1pbmF0ZS1iZy1jb2xvcjtcbiRmb3JtLWNoZWNrLWlucHV0LWluZGV0ZXJtaW5hdGUtYmctaW1hZ2U6IHVybChcImRhdGE6aW1hZ2Uvc3ZnK3htbCw8c3ZnIHhtbG5zPSdodHRwOi8vd3d3LnczLm9yZy8yMDAwL3N2Zycgdmlld0JveD0nMCAwIDIwIDIwJz48cGF0aCBmaWxsPSdub25lJyBzdHJva2U9JyN7JGZvcm0tY2hlY2staW5wdXQtaW5kZXRlcm1pbmF0ZS1jb2xvcn0nIHN0cm9rZS1saW5lY2FwPSdyb3VuZCcgc3Ryb2tlLWxpbmVqb2luPSdyb3VuZCcgc3Ryb2tlLXdpZHRoPSczJyBkPSdNNiAxMGg4Jy8+PC9zdmc+XCIpO1xuXG4kZm9ybS1jaGVjay1pbnB1dC1kaXNhYmxlZC1vcGFjaXR5OiAwLjU7XG4kZm9ybS1jaGVjay1sYWJlbC1kaXNhYmxlZC1vcGFjaXR5OiAkZm9ybS1jaGVjay1pbnB1dC1kaXNhYmxlZC1vcGFjaXR5O1xuJGZvcm0tY2hlY2stYnRuLWNoZWNrLWRpc2FibGVkLW9wYWNpdHk6ICRidG4tZGlzYWJsZWQtb3BhY2l0eTtcbiRmb3JtLXN3aXRjaC1jb2xvcjogcmdiYSgwLCAwLCAwLCAwLjI1KTtcbiRmb3JtLXN3aXRjaC13aWR0aDogMmVtO1xuJGZvcm0tc3dpdGNoLXBhZGRpbmctc3RhcnQ6ICRmb3JtLXN3aXRjaC13aWR0aCArIDAuNWVtO1xuJGZvcm0tc3dpdGNoLWJnLWltYWdlOiB1cmwoXCJkYXRhOmltYWdlL3N2Zyt4bWwsPHN2ZyB4bWxucz0naHR0cDovL3d3dy53My5vcmcvMjAwMC9zdmcnIHZpZXdCb3g9Jy00IC00IDggOCc+PGNpcmNsZSByPSczJyBmaWxsPScjeyRmb3JtLXN3aXRjaC1jb2xvcn0nLz48L3N2Zz5cIik7XG4kZm9ybS1zd2l0Y2gtYm9yZGVyLXJhZGl1czogJGZvcm0tc3dpdGNoLXdpZHRoO1xuXG4kZm9ybS1zd2l0Y2gtZm9jdXMtY29sb3I6ICRpbnB1dC1mb2N1cy1ib3JkZXItY29sb3I7XG4kZm9ybS1zd2l0Y2gtZm9jdXMtYmctaW1hZ2U6IHVybChcImRhdGE6aW1hZ2Uvc3ZnK3htbCw8c3ZnIHhtbG5zPSdodHRwOi8vd3d3LnczLm9yZy8yMDAwL3N2Zycgdmlld0JveD0nLTQgLTQgOCA4Jz48Y2lyY2xlIHI9JzMnIGZpbGw9JyN7JGZvcm0tc3dpdGNoLWZvY3VzLWNvbG9yfScvPjwvc3ZnPlwiKTtcblxuJGZvcm0tc3dpdGNoLWNoZWNrZWQtY29sb3I6ICRjb21wb25lbnQtYWN0aXZlLWNvbG9yO1xuJGZvcm0tc3dpdGNoLWNoZWNrZWQtYmctaW1hZ2U6IHVybChcImRhdGE6aW1hZ2Uvc3ZnK3htbCw8c3ZnIHhtbG5zPSdodHRwOi8vd3d3LnczLm9yZy8yMDAwL3N2Zycgdmlld0JveD0nLTQgLTQgOCA4Jz48Y2lyY2xlIHI9JzMnIGZpbGw9JyN7JGZvcm0tc3dpdGNoLWNoZWNrZWQtY29sb3J9Jy8+PC9zdmc+XCIpO1xuJGZvcm0tc3dpdGNoLWNoZWNrZWQtYmctcG9zaXRpb246IHJpZ2h0IGNlbnRlcjtcblxuJGlucHV0LWdyb3VwLWFkZG9uLXBhZGRpbmcteTogJGlucHV0LXBhZGRpbmcteTtcbiRpbnB1dC1ncm91cC1hZGRvbi1wYWRkaW5nLXg6ICRpbnB1dC1wYWRkaW5nLXg7XG4kaW5wdXQtZ3JvdXAtYWRkb24tZm9udC13ZWlnaHQ6ICRpbnB1dC1mb250LXdlaWdodDtcbiRpbnB1dC1ncm91cC1hZGRvbi1jb2xvcjogJGlucHV0LWNvbG9yO1xuJGlucHV0LWdyb3VwLWFkZG9uLWJnOiAkYm9keS1iZztcbiRpbnB1dC1ncm91cC1hZGRvbi1ib3JkZXItY29sb3I6ICRpbnB1dC1ib3JkZXItY29sb3I7XG5cbiRmb3JtLXNlbGVjdC1wYWRkaW5nLXk6ICRpbnB1dC1wYWRkaW5nLXk7XG4kZm9ybS1zZWxlY3QtcGFkZGluZy14OiAkaW5wdXQtcGFkZGluZy14O1xuJGZvcm0tc2VsZWN0LWZvbnQtZmFtaWx5OiAkaW5wdXQtZm9udC1mYW1pbHk7XG4kZm9ybS1zZWxlY3QtZm9udC1zaXplOiAkaW5wdXQtZm9udC1zaXplO1xuJGZvcm0tc2VsZWN0LWluZGljYXRvci1wYWRkaW5nOiAxcmVtOyAvLyBFeHRyYSBwYWRkaW5nIHRvIGFjY291bnQgZm9yIHRoZSBwcmVzZW5jZSBvZiB0aGUgYmFja2dyb3VuZC1pbWFnZSBiYXNlZCBpbmRpY2F0b3JcbiRmb3JtLXNlbGVjdC1mb250LXdlaWdodDogJGlucHV0LWZvbnQtd2VpZ2h0O1xuJGZvcm0tc2VsZWN0LWxpbmUtaGVpZ2h0OiAkaW5wdXQtbGluZS1oZWlnaHQ7XG4kZm9ybS1zZWxlY3QtY29sb3I6ICRpbnB1dC1jb2xvcjtcbiRmb3JtLXNlbGVjdC1kaXNhYmxlZC1jb2xvcjogJGdyYXktNjAwO1xuJGZvcm0tc2VsZWN0LWJnOiAkaW5wdXQtYmc7XG4kZm9ybS1zZWxlY3QtZGlzYWJsZWQtYmc6ICRncmF5LTIwMDtcbiRmb3JtLXNlbGVjdC1kaXNhYmxlZC1ib3JkZXItY29sb3I6ICRpbnB1dC1kaXNhYmxlZC1ib3JkZXItY29sb3I7XG4kZm9ybS1zZWxlY3QtYmctcG9zaXRpb246IHJpZ2h0ICRmb3JtLXNlbGVjdC1wYWRkaW5nLXggY2VudGVyO1xuJGZvcm0tc2VsZWN0LWJnLXNpemU6IDE2cHggMTJweDsgLy8gSW4gcGl4ZWxzIGJlY2F1c2UgaW1hZ2UgZGltZW5zaW9uc1xuJGZvcm0tc2VsZWN0LWluZGljYXRvci1jb2xvcjogJGdyYXktODAwO1xuJGZvcm0tc2VsZWN0LWluZGljYXRvcjogdXJsKFwiZGF0YTppbWFnZS9zdmcreG1sLDxzdmcgeG1sbnM9J2h0dHA6Ly93d3cudzMub3JnLzIwMDAvc3ZnJyB2aWV3Qm94PScwIDAgMTYgMTYnPjxwYXRoIGZpbGw9J25vbmUnIHN0cm9rZT0nI3skZm9ybS1zZWxlY3QtaW5kaWNhdG9yLWNvbG9yfScgc3Ryb2tlLWxpbmVjYXA9J3JvdW5kJyBzdHJva2UtbGluZWpvaW49J3JvdW5kJyBzdHJva2Utd2lkdGg9JzInIGQ9J00yIDVsNiA2IDYtNicvPjwvc3ZnPlwiKTtcblxuJGZvcm0tc2VsZWN0LWZlZWRiYWNrLWljb24tcGFkZGluZy1lbmQ6IGFkZChcbiAgMWVtICogMC43NSxcbiAgKDIgKiAkZm9ybS1zZWxlY3QtcGFkZGluZy15ICogMC43NSkgKyAkZm9ybS1zZWxlY3QtcGFkZGluZy14ICtcbiAgICAkZm9ybS1zZWxlY3QtaW5kaWNhdG9yLXBhZGRpbmdcbik7XG4kZm9ybS1zZWxlY3QtZmVlZGJhY2staWNvbi1wb3NpdGlvbjogY2VudGVyIHJpZ2h0XG4gICgkZm9ybS1zZWxlY3QtcGFkZGluZy14ICsgJGZvcm0tc2VsZWN0LWluZGljYXRvci1wYWRkaW5nKTtcbiRmb3JtLXNlbGVjdC1mZWVkYmFjay1pY29uLXNpemU6ICRpbnB1dC1oZWlnaHQtaW5uZXItaGFsZlxuICAkaW5wdXQtaGVpZ2h0LWlubmVyLWhhbGY7XG5cbiRmb3JtLXNlbGVjdC1ib3JkZXItd2lkdGg6ICRpbnB1dC1ib3JkZXItd2lkdGg7XG4kZm9ybS1zZWxlY3QtYm9yZGVyLWNvbG9yOiAkaW5wdXQtYm9yZGVyLWNvbG9yO1xuJGZvcm0tc2VsZWN0LWJvcmRlci1yYWRpdXM6IDZweDtcbiRmb3JtLXNlbGVjdC1ib3gtc2hhZG93OiAkYm94LXNoYWRvdy1pbnNldDtcblxuJGZvcm0tc2VsZWN0LWZvY3VzLWJvcmRlci1jb2xvcjogJGlucHV0LWZvY3VzLWJvcmRlci1jb2xvcjtcbiRmb3JtLXNlbGVjdC1mb2N1cy13aWR0aDogJGlucHV0LWZvY3VzLXdpZHRoO1xuJGZvcm0tc2VsZWN0LWZvY3VzLWJveC1zaGFkb3c6IDAgMCAwICRmb3JtLXNlbGVjdC1mb2N1cy13aWR0aFxuICAkaW5wdXQtYnRuLWZvY3VzLWNvbG9yO1xuXG4kZm9ybS1zZWxlY3QtcGFkZGluZy15LXNtOiAkaW5wdXQtcGFkZGluZy15LXNtO1xuJGZvcm0tc2VsZWN0LXBhZGRpbmcteC1zbTogJGlucHV0LXBhZGRpbmcteC1zbTtcbiRmb3JtLXNlbGVjdC1mb250LXNpemUtc206ICRpbnB1dC1mb250LXNpemUtc207XG4kZm9ybS1zZWxlY3QtYm9yZGVyLXJhZGl1cy1zbTogNHB4O1xuXG4kZm9ybS1zZWxlY3QtcGFkZGluZy15LWxnOiAkaW5wdXQtcGFkZGluZy15LWxnO1xuJGZvcm0tc2VsZWN0LXBhZGRpbmcteC1sZzogJGlucHV0LXBhZGRpbmcteC1sZztcbiRmb3JtLXNlbGVjdC1mb250LXNpemUtbGc6ICRpbnB1dC1mb250LXNpemUtbGc7XG4kZm9ybS1zZWxlY3QtYm9yZGVyLXJhZGl1cy1sZzogOHB4O1xuXG4kZm9ybS1yYW5nZS10cmFjay13aWR0aDogMTAwJTtcbiRmb3JtLXJhbmdlLXRyYWNrLWhlaWdodDogMC41cmVtO1xuJGZvcm0tcmFuZ2UtdHJhY2stY3Vyc29yOiBwb2ludGVyO1xuJGZvcm0tcmFuZ2UtdHJhY2stYmc6ICRib2R5LWJnO1xuJGZvcm0tcmFuZ2UtdHJhY2stYm9yZGVyLXJhZGl1czogMXJlbTtcbiRmb3JtLXJhbmdlLXRyYWNrLWJveC1zaGFkb3c6ICRib3gtc2hhZG93LWluc2V0O1xuXG4kZm9ybS1yYW5nZS10aHVtYi13aWR0aDogMXJlbTtcbiRmb3JtLXJhbmdlLXRodW1iLWhlaWdodDogJGZvcm0tcmFuZ2UtdGh1bWItd2lkdGg7XG4kZm9ybS1yYW5nZS10aHVtYi1iZzogJGNvbXBvbmVudC1hY3RpdmUtYmc7XG4kZm9ybS1yYW5nZS10aHVtYi1ib3JkZXI6IDA7XG4kZm9ybS1yYW5nZS10aHVtYi1ib3JkZXItcmFkaXVzOiAxcmVtO1xuJGZvcm0tcmFuZ2UtdGh1bWItYm94LXNoYWRvdzogMCAwLjFyZW0gMC4yNXJlbSByZ2JhKCRibGFjaywgMC4xKTtcbiRmb3JtLXJhbmdlLXRodW1iLWZvY3VzLWJveC1zaGFkb3c6IDAgMCAwIDFweCAkYm9keS1iZywgJGlucHV0LWZvY3VzLWJveC1zaGFkb3c7XG4kZm9ybS1yYW5nZS10aHVtYi1mb2N1cy1ib3gtc2hhZG93LXdpZHRoOiAkaW5wdXQtZm9jdXMtd2lkdGg7IC8vIEZvciBmb2N1cyBib3ggc2hhZG93IGlzc3VlIGluIEVkZ2VcbiRmb3JtLXJhbmdlLXRodW1iLWFjdGl2ZS1iZzogbGlnaHRlbigkY29tcG9uZW50LWFjdGl2ZS1iZywgMzUlKTtcbiRmb3JtLXJhbmdlLXRodW1iLWRpc2FibGVkLWJnOiAkZ3JheS01MDA7XG4kZm9ybS1yYW5nZS10aHVtYi10cmFuc2l0aW9uOiBiYWNrZ3JvdW5kLWNvbG9yIDAuMTVzIGVhc2UtaW4tb3V0LFxuICBib3JkZXItY29sb3IgMC4xNXMgZWFzZS1pbi1vdXQsIGJveC1zaGFkb3cgMC4xNXMgZWFzZS1pbi1vdXQ7XG5cbiRmb3JtLWZpbGUtYnV0dG9uLWNvbG9yOiAkaW5wdXQtY29sb3I7XG4kZm9ybS1maWxlLWJ1dHRvbi1iZzogJGlucHV0LWdyb3VwLWFkZG9uLWJnO1xuJGZvcm0tZmlsZS1idXR0b24taG92ZXItYmc6IHNoYWRlLWNvbG9yKCRmb3JtLWZpbGUtYnV0dG9uLWJnLCA1JSk7XG5cbiRmb3JtLWZsb2F0aW5nLWhlaWdodDogYWRkKDMuNnJlbSwgJGlucHV0LWhlaWdodC1ib3JkZXIpO1xuJGZvcm0tZmxvYXRpbmctcGFkZGluZy14OiAkaW5wdXQtcGFkZGluZy14O1xuJGZvcm0tZmxvYXRpbmctcGFkZGluZy15OiAxcmVtO1xuJGZvcm0tZmxvYXRpbmctaW5wdXQtcGFkZGluZy10OiAxLjYyNXJlbTtcbiRmb3JtLWZsb2F0aW5nLWlucHV0LXBhZGRpbmctYjogMC42MjVyZW07XG4kZm9ybS1mbG9hdGluZy1sYWJlbC1vcGFjaXR5OiAwLjY1O1xuJGZvcm0tZmxvYXRpbmctbGFiZWwtdHJhbnNmb3JtOiBzY2FsZSgwLjg1KSB0cmFuc2xhdGVZKC0wLjVyZW0pXG4gIHRyYW5zbGF0ZVgoMC4xNXJlbSk7XG4kZm9ybS1mbG9hdGluZy10cmFuc2l0aW9uOiBvcGFjaXR5IDAuMXMgZWFzZS1pbi1vdXQsIHRyYW5zZm9ybSAwLjFzIGVhc2UtaW4tb3V0O1xuXG4vLyBGb3JtIHZhbGlkYXRpb25cblxuJGZvcm0tZmVlZGJhY2stbWFyZ2luLXRvcDogJGZvcm0tdGV4dC1tYXJnaW4tdG9wO1xuJGZvcm0tZmVlZGJhY2stZm9udC1zaXplOiAkZm9ybS10ZXh0LWZvbnQtc2l6ZTtcbiRmb3JtLWZlZWRiYWNrLWZvbnQtc3R5bGU6ICRmb3JtLXRleHQtZm9udC1zdHlsZTtcbiRmb3JtLWZlZWRiYWNrLXZhbGlkLWNvbG9yOiAkc3VjY2VzcztcbiRmb3JtLWZlZWRiYWNrLWludmFsaWQtY29sb3I6ICRkYW5nZXI7XG5cbiRmb3JtLWZlZWRiYWNrLWljb24tdmFsaWQtY29sb3I6ICRmb3JtLWZlZWRiYWNrLXZhbGlkLWNvbG9yO1xuJGZvcm0tZmVlZGJhY2staWNvbi12YWxpZDogc3RyLXJlcGxhY2UoXG4gIHVybChcImRhdGE6aW1hZ2Uvc3ZnK3htbCwlM2NzdmcgeG1sbnM9J2h0dHA6Ly93d3cudzMub3JnLzIwMDAvc3ZnJyB2aWV3Qm94PScwIDAgOCA4JyUzZSUzY3BhdGggZmlsbD0nI3skZm9ybS1mZWVkYmFjay1pY29uLXZhbGlkLWNvbG9yfScgZD0nTTIuMyA2LjczTC42IDQuNTNjLS40LTEuMDQuNDYtMS40IDEuMS0uOGwxLjEgMS40IDMuNC0zLjhjLjYtLjYzIDEuNi0uMjcgMS4yLjdsLTQgNC42Yy0uNDMuNS0uOC40LTEuMS4xeicvJTNlJTNjL3N2ZyUzZVwiKSxcbiAgXCIjXCIsXG4gIFwiJTIzXCJcbik7XG4kZm9ybS1mZWVkYmFjay1pY29uLWludmFsaWQtY29sb3I6ICRmb3JtLWZlZWRiYWNrLWludmFsaWQtY29sb3I7XG4kZm9ybS1mZWVkYmFjay1pY29uLWludmFsaWQ6IHN0ci1yZXBsYWNlKFxuICB1cmwoXCJkYXRhOmltYWdlL3N2Zyt4bWwsJTNjc3ZnIHhtbG5zPSdodHRwOi8vd3d3LnczLm9yZy8yMDAwL3N2ZycgZmlsbD0nI3skZm9ybS1mZWVkYmFjay1pY29uLWludmFsaWQtY29sb3J9JyB2aWV3Qm94PSctMiAtMiA3IDcnJTNlJTNjcGF0aCBzdHJva2U9JyN7JGZvcm0tZmVlZGJhY2staWNvbi1pbnZhbGlkLWNvbG9yfScgZD0nTTAgMGwzIDNtMC0zTDAgMycvJTNlJTNjY2lyY2xlIHI9Jy41Jy8lM2UlM2NjaXJjbGUgY3g9JzMnIHI9Jy41Jy8lM2UlM2NjaXJjbGUgY3k9JzMnIHI9Jy41Jy8lM2UlM2NjaXJjbGUgY3g9JzMnIGN5PSczJyByPScuNScvJTNlJTNjL3N2ZyUzRVwiKSxcbiAgXCIjXCIsXG4gIFwiJTIzXCJcbik7XG5cbi8vIHNjc3MtZG9jcy1zdGFydCBmb3JtLXZhbGlkYXRpb24tc3RhdGVzXG4kZm9ybS12YWxpZGF0aW9uLXN0YXRlczogKFxuICBcInZhbGlkXCI6IChcbiAgICBcImNvbG9yXCI6ICRmb3JtLWZlZWRiYWNrLXZhbGlkLWNvbG9yLFxuICAgIFwiaWNvblwiOiAkZm9ybS1mZWVkYmFjay1pY29uLXZhbGlkLFxuICApLFxuICBcImludmFsaWRcIjogKFxuICAgIFwiY29sb3JcIjogJGZvcm0tZmVlZGJhY2staW52YWxpZC1jb2xvcixcbiAgICBcImljb25cIjogJGZvcm0tZmVlZGJhY2staWNvbi1pbnZhbGlkLFxuICApLFxuKTtcbi8vIHNjc3MtZG9jcy1lbmQgZm9ybS12YWxpZGF0aW9uLXN0YXRlc1xuXG4vLyBaLWluZGV4IG1hc3RlciBsaXN0XG4vL1xuLy8gV2FybmluZzogQXZvaWQgY3VzdG9taXppbmcgdGhlc2UgdmFsdWVzLiBUaGV5J3JlIHVzZWQgZm9yIGEgYmlyZCdzIGV5ZSB2aWV3XG4vLyBvZiBjb21wb25lbnRzIGRlcGVuZGVudCBvbiB0aGUgei1heGlzIGFuZCBhcmUgZGVzaWduZWQgdG8gYWxsIHdvcmsgdG9nZXRoZXIuXG5cbi8vIHNjc3MtZG9jcy1zdGFydCB6aW5kZXgtc3RhY2tcbiR6aW5kZXgtZHJvcGRvd246IDEwMDA7XG4kemluZGV4LXN0aWNreTogMTAyMDtcbiR6aW5kZXgtZml4ZWQ6IDEwMzA7XG4kemluZGV4LW9mZmNhbnZhcy1iYWNrZHJvcDogMTA0MDtcbiR6aW5kZXgtb2ZmY2FudmFzOiAxMDQ1O1xuJHppbmRleC1tb2RhbC1iYWNrZHJvcDogMTA1MDtcbiR6aW5kZXgtbW9kYWw6IDEwNTU7XG4kemluZGV4LXBvcG92ZXI6IDEwNzA7XG4kemluZGV4LXRvb2x0aXA6IDEwODA7XG4vLyBzY3NzLWRvY3MtZW5kIHppbmRleC1zdGFja1xuXG4vLyBOYXZzXG5cbiRuYXYtbGluay1wYWRkaW5nLXk6IDAuNXJlbTtcbiRuYXYtbGluay1wYWRkaW5nLXg6IDFyZW07XG4kbmF2LWxpbmstZm9udC1zaXplOiBudWxsO1xuJG5hdi1saW5rLWZvbnQtd2VpZ2h0OiBudWxsO1xuJG5hdi1saW5rLWNvbG9yOiBudWxsO1xuJG5hdi1saW5rLWhvdmVyLWNvbG9yOiBudWxsO1xuJG5hdi1saW5rLXRyYW5zaXRpb246IGNvbG9yIDAuMTVzIGVhc2UtaW4tb3V0LFxuICBiYWNrZ3JvdW5kLWNvbG9yIDAuMTVzIGVhc2UtaW4tb3V0LCBib3JkZXItY29sb3IgMC4xNXMgZWFzZS1pbi1vdXQ7XG4kbmF2LWxpbmstZGlzYWJsZWQtY29sb3I6ICRncmF5LTYwMDtcblxuJG5hdi10YWJzLWJvcmRlci1jb2xvcjogJGdyYXktMzAwO1xuJG5hdi10YWJzLWJvcmRlci13aWR0aDogJGJvcmRlci13aWR0aDtcbiRuYXYtdGFicy1ib3JkZXItcmFkaXVzOiAkYm9yZGVyLXJhZGl1cztcbiRuYXYtdGFicy1saW5rLWhvdmVyLWJvcmRlci1jb2xvcjogJGdyYXktMjAwICRncmF5LTIwMCAkbmF2LXRhYnMtYm9yZGVyLWNvbG9yO1xuJG5hdi10YWJzLWxpbmstYWN0aXZlLWNvbG9yOiAkZ3JheS03MDA7XG4kbmF2LXRhYnMtbGluay1hY3RpdmUtYmc6ICR3aGl0ZTsgLy8gY2hhbmdlXG4kbmF2LXRhYnMtbGluay1hY3RpdmUtYm9yZGVyLWNvbG9yOiAkZ3JheS0zMDAgJGdyYXktMzAwICRuYXYtdGFicy1saW5rLWFjdGl2ZS1iZztcblxuJG5hdi1waWxscy1ib3JkZXItcmFkaXVzOiAkYm9yZGVyLXJhZGl1cztcbiRuYXYtcGlsbHMtbGluay1hY3RpdmUtY29sb3I6ICRjb21wb25lbnQtYWN0aXZlLWNvbG9yO1xuJG5hdi1waWxscy1saW5rLWFjdGl2ZS1iZzogJGNvbXBvbmVudC1hY3RpdmUtYmc7XG5cbi8vIE5hdmJhclxuXG4kbmF2YmFyLXBhZGRpbmcteTogJHNwYWNlciAvIDI7XG4kbmF2YmFyLXBhZGRpbmcteDogbnVsbDtcblxuJG5hdmJhci1uYXYtbGluay1wYWRkaW5nLXg6IDAuNXJlbTtcblxuJG5hdmJhci1icmFuZC1mb250LXNpemU6ICRmb250LXNpemUtbGc7XG4vLyBDb21wdXRlIHRoZSBuYXZiYXItYnJhbmQgcGFkZGluZy15IHNvIHRoZSBuYXZiYXItYnJhbmQgd2lsbCBoYXZlIHRoZSBzYW1lIGhlaWdodCBhcyBuYXZiYXItdGV4dCBhbmQgbmF2LWxpbmtcbiRuYXYtbGluay1oZWlnaHQ6ICRmb250LXNpemUtYmFzZSAqICRsaW5lLWhlaWdodC1iYXNlICsgJG5hdi1saW5rLXBhZGRpbmcteSAqIDI7XG4kbmF2YmFyLWJyYW5kLWhlaWdodDogJG5hdmJhci1icmFuZC1mb250LXNpemUgKiAkbGluZS1oZWlnaHQtYmFzZTtcbiRuYXZiYXItYnJhbmQtcGFkZGluZy15OiAoJG5hdi1saW5rLWhlaWdodCAtICRuYXZiYXItYnJhbmQtaGVpZ2h0KSAvIDI7XG4kbmF2YmFyLWJyYW5kLW1hcmdpbi1lbmQ6IDFyZW07XG5cbiRuYXZiYXItdG9nZ2xlci1wYWRkaW5nLXk6IDAuMjVyZW07XG4kbmF2YmFyLXRvZ2dsZXItcGFkZGluZy14OiAwLjc1cmVtO1xuJG5hdmJhci10b2dnbGVyLWZvbnQtc2l6ZTogJGZvbnQtc2l6ZS1sZztcbiRuYXZiYXItdG9nZ2xlci1ib3JkZXItcmFkaXVzOiAkYnRuLWJvcmRlci1yYWRpdXM7XG4kbmF2YmFyLXRvZ2dsZXItZm9jdXMtd2lkdGg6ICRidG4tZm9jdXMtd2lkdGg7XG4kbmF2YmFyLXRvZ2dsZXItdHJhbnNpdGlvbjogYm94LXNoYWRvdyAwLjE1cyBlYXNlLWluLW91dDtcblxuJG5hdmJhci1kYXJrLWNvbG9yOiByZ2JhKCR3aGl0ZSwgMC41NSk7XG4kbmF2YmFyLWRhcmstaG92ZXItY29sb3I6IHJnYmEoJHdoaXRlLCAwLjc1KTtcbiRuYXZiYXItZGFyay1hY3RpdmUtY29sb3I6ICR3aGl0ZTtcbiRuYXZiYXItZGFyay1kaXNhYmxlZC1jb2xvcjogcmdiYSgkd2hpdGUsIDAuMjUpO1xuJG5hdmJhci1kYXJrLXRvZ2dsZXItaWNvbi1iZzogdXJsKFwiZGF0YTppbWFnZS9zdmcreG1sLDxzdmcgeG1sbnM9J2h0dHA6Ly93d3cudzMub3JnLzIwMDAvc3ZnJyB2aWV3Qm94PScwIDAgMzAgMzAnPjxwYXRoIHN0cm9rZT0nI3skbmF2YmFyLWRhcmstY29sb3J9JyBzdHJva2UtbGluZWNhcD0ncm91bmQnIHN0cm9rZS1taXRlcmxpbWl0PScxMCcgc3Ryb2tlLXdpZHRoPScyJyBkPSdNNCA3aDIyTTQgMTVoMjJNNCAyM2gyMicvPjwvc3ZnPlwiKTtcbiRuYXZiYXItZGFyay10b2dnbGVyLWJvcmRlci1jb2xvcjogcmdiYSgkd2hpdGUsIDAuMSk7XG5cbiRuYXZiYXItbGlnaHQtY29sb3I6IHJnYmEoJGJsYWNrLCAwLjU1KTtcbiRuYXZiYXItbGlnaHQtaG92ZXItY29sb3I6IHJnYmEoJGJsYWNrLCAwLjcpO1xuJG5hdmJhci1saWdodC1hY3RpdmUtY29sb3I6IHJnYmEoJGJsYWNrLCAwLjkpO1xuJG5hdmJhci1saWdodC1kaXNhYmxlZC1jb2xvcjogcmdiYSgkYmxhY2ssIDAuMyk7XG4kbmF2YmFyLWxpZ2h0LXRvZ2dsZXItaWNvbi1iZzogdXJsKFwiZGF0YTppbWFnZS9zdmcreG1sLDxzdmcgeG1sbnM9J2h0dHA6Ly93d3cudzMub3JnLzIwMDAvc3ZnJyB2aWV3Qm94PScwIDAgMzAgMzAnPjxwYXRoIHN0cm9rZT0nI3skbmF2YmFyLWxpZ2h0LWNvbG9yfScgc3Ryb2tlLWxpbmVjYXA9J3JvdW5kJyBzdHJva2UtbWl0ZXJsaW1pdD0nMTAnIHN0cm9rZS13aWR0aD0nMicgZD0nTTQgN2gyMk00IDE1aDIyTTQgMjNoMjInLz48L3N2Zz5cIik7XG4kbmF2YmFyLWxpZ2h0LXRvZ2dsZXItYm9yZGVyLWNvbG9yOiByZ2JhKCRibGFjaywgMC4xKTtcblxuJG5hdmJhci1saWdodC1icmFuZC1jb2xvcjogJG5hdmJhci1saWdodC1hY3RpdmUtY29sb3I7XG4kbmF2YmFyLWxpZ2h0LWJyYW5kLWhvdmVyLWNvbG9yOiAkbmF2YmFyLWxpZ2h0LWFjdGl2ZS1jb2xvcjtcbiRuYXZiYXItZGFyay1icmFuZC1jb2xvcjogJG5hdmJhci1kYXJrLWFjdGl2ZS1jb2xvcjtcbiRuYXZiYXItZGFyay1icmFuZC1ob3Zlci1jb2xvcjogJG5hdmJhci1kYXJrLWFjdGl2ZS1jb2xvcjtcblxuLy8gRHJvcGRvd25zXG4vL1xuLy8gRHJvcGRvd24gbWVudSBjb250YWluZXIgYW5kIGNvbnRlbnRzLlxuXG4kZHJvcGRvd24tbWluLXdpZHRoOiAxMnJlbTtcbiRkcm9wZG93bi1wYWRkaW5nLXg6IDA7XG4kZHJvcGRvd24tcGFkZGluZy15OiAwLjVyZW07XG4kZHJvcGRvd24tc3BhY2VyOiAwLjEyNXJlbTtcbiRkcm9wZG93bi1mb250LXNpemU6ICRmb250LXNpemUtYmFzZTtcbiRkcm9wZG93bi1jb2xvcjogJGJvZHktY29sb3I7XG4kZHJvcGRvd24tYmc6ICR3aGl0ZTtcbiRkcm9wZG93bi1ib3JkZXItY29sb3I6IHJnYmEoJGJsYWNrLCAwLjE1KTtcbiRkcm9wZG93bi1ib3JkZXItcmFkaXVzOiAkYm9yZGVyLXJhZGl1cztcbiRkcm9wZG93bi1ib3JkZXItd2lkdGg6IDA7XG4kZHJvcGRvd24taW5uZXItYm9yZGVyLXJhZGl1czogc3VidHJhY3QoXG4gICRkcm9wZG93bi1ib3JkZXItcmFkaXVzLFxuICAkZHJvcGRvd24tYm9yZGVyLXdpZHRoXG4pO1xuJGRyb3Bkb3duLWRpdmlkZXItYmc6ICRncmF5LTIwMDtcbiRkcm9wZG93bi1kaXZpZGVyLW1hcmdpbi15OiAkc3BhY2VyIC8gMjtcbiRkcm9wZG93bi1ib3gtc2hhZG93OiAwIDAuNXJlbSAxcmVtIHJnYmEoJGJsYWNrLCAwLjE3NSk7XG5cbiRkcm9wZG93bi1saW5rLWNvbG9yOiAkYm9keS1jb2xvcjtcbiRkcm9wZG93bi1saW5rLWhvdmVyLWNvbG9yOiAkZ3JheS04MDA7XG4kZHJvcGRvd24tbGluay1ob3Zlci1iZzogc2hpZnQtY29sb3IoJGdyYXktNTAwLCAtODAlKTtcblxuJGRyb3Bkb3duLWxpbmstYWN0aXZlLWNvbG9yOiAkZ3JheS04MDA7XG4kZHJvcGRvd24tbGluay1hY3RpdmUtYmc6IHNoaWZ0LWNvbG9yKCRncmF5LTUwMCwgLTgwJSk7XG5cbiRkcm9wZG93bi1saW5rLWRpc2FibGVkLWNvbG9yOiAkZ3JheS02MDA7XG5cbiRkcm9wZG93bi1pdGVtLXBhZGRpbmcteTogMC42NXJlbTtcbiRkcm9wZG93bi1pdGVtLXBhZGRpbmcteDogMS41cmVtO1xuXG4kZHJvcGRvd24taGVhZGVyLWNvbG9yOiAkZ3JheS02MDA7XG4kZHJvcGRvd24taGVhZGVyLXBhZGRpbmc6ICRkcm9wZG93bi1wYWRkaW5nLXkgJGRyb3Bkb3duLWl0ZW0tcGFkZGluZy14O1xuXG4kZHJvcGRvd24tZGFyay1jb2xvcjogJGdyYXktMzAwO1xuJGRyb3Bkb3duLWRhcmstYmc6ICRncmF5LTgwMDtcbiRkcm9wZG93bi1kYXJrLWJvcmRlci1jb2xvcjogJGRyb3Bkb3duLWJvcmRlci1jb2xvcjtcbiRkcm9wZG93bi1kYXJrLWRpdmlkZXItYmc6ICRkcm9wZG93bi1kaXZpZGVyLWJnO1xuJGRyb3Bkb3duLWRhcmstYm94LXNoYWRvdzogbnVsbDtcbiRkcm9wZG93bi1kYXJrLWxpbmstY29sb3I6ICRkcm9wZG93bi1kYXJrLWNvbG9yO1xuJGRyb3Bkb3duLWRhcmstbGluay1ob3Zlci1jb2xvcjogJHdoaXRlO1xuJGRyb3Bkb3duLWRhcmstbGluay1ob3Zlci1iZzogcmdiYSgkd2hpdGUsIDAuMTUpO1xuJGRyb3Bkb3duLWRhcmstbGluay1hY3RpdmUtY29sb3I6ICRkcm9wZG93bi1saW5rLWFjdGl2ZS1jb2xvcjtcbiRkcm9wZG93bi1kYXJrLWxpbmstYWN0aXZlLWJnOiAkZHJvcGRvd24tbGluay1hY3RpdmUtYmc7XG4kZHJvcGRvd24tZGFyay1saW5rLWRpc2FibGVkLWNvbG9yOiAkZ3JheS01MDA7XG4kZHJvcGRvd24tZGFyay1oZWFkZXItY29sb3I6ICRncmF5LTUwMDtcblxuLy8gUGFnaW5hdGlvblxuXG4kcGFnaW5hdGlvbi1wYWRkaW5nLXk6IDAuMzc1cmVtO1xuJHBhZ2luYXRpb24tcGFkZGluZy14OiAwLjc1cmVtO1xuJHBhZ2luYXRpb24tcGFkZGluZy15LXNtOiAwLjI1cmVtO1xuJHBhZ2luYXRpb24tcGFkZGluZy14LXNtOiAwLjVyZW07XG4kcGFnaW5hdGlvbi1wYWRkaW5nLXktbGc6IDAuNzVyZW07XG4kcGFnaW5hdGlvbi1wYWRkaW5nLXgtbGc6IDEuNXJlbTtcblxuJHBhZ2luYXRpb24tY29sb3I6ICRwcmltYXJ5O1xuJHBhZ2luYXRpb24tYmc6ICR3aGl0ZTtcbiRwYWdpbmF0aW9uLWJvcmRlci13aWR0aDogJGJvcmRlci13aWR0aDtcbiRwYWdpbmF0aW9uLWJvcmRlci1yYWRpdXM6ICRib3JkZXItcmFkaXVzO1xuJHBhZ2luYXRpb24tbWFyZ2luLXN0YXJ0OiAtJHBhZ2luYXRpb24tYm9yZGVyLXdpZHRoO1xuJHBhZ2luYXRpb24tYm9yZGVyLWNvbG9yOiAkZ3JheS0zMDA7XG5cbiRwYWdpbmF0aW9uLWZvY3VzLWNvbG9yOiAkbGluay1ob3Zlci1jb2xvcjtcbiRwYWdpbmF0aW9uLWZvY3VzLWJnOiAkZ3JheS0yMDA7XG4kcGFnaW5hdGlvbi1mb2N1cy1ib3gtc2hhZG93OiAkaW5wdXQtYnRuLWZvY3VzLWJveC1zaGFkb3c7XG4kcGFnaW5hdGlvbi1mb2N1cy1vdXRsaW5lOiAwO1xuXG4kcGFnaW5hdGlvbi1ob3Zlci1jb2xvcjogJGxpbmstaG92ZXItY29sb3I7XG4kcGFnaW5hdGlvbi1ob3Zlci1iZzogJGJvZHktYmc7XG4kcGFnaW5hdGlvbi1ob3Zlci1ib3JkZXItY29sb3I6ICRncmF5LTMwMDtcblxuJHBhZ2luYXRpb24tYWN0aXZlLWNvbG9yOiAkY29tcG9uZW50LWFjdGl2ZS1jb2xvcjtcbiRwYWdpbmF0aW9uLWFjdGl2ZS1iZzogJGNvbXBvbmVudC1hY3RpdmUtYmc7XG4kcGFnaW5hdGlvbi1hY3RpdmUtYm9yZGVyLWNvbG9yOiAkcGFnaW5hdGlvbi1hY3RpdmUtYmc7XG5cbiRwYWdpbmF0aW9uLWRpc2FibGVkLWNvbG9yOiAkZ3JheS02MDA7XG4kcGFnaW5hdGlvbi1kaXNhYmxlZC1iZzogJHdoaXRlO1xuJHBhZ2luYXRpb24tZGlzYWJsZWQtYm9yZGVyLWNvbG9yOiAkZ3JheS0zMDA7XG5cbiRwYWdpbmF0aW9uLXRyYW5zaXRpb246IGNvbG9yIDAuMTVzIGVhc2UtaW4tb3V0LFxuICBiYWNrZ3JvdW5kLWNvbG9yIDAuMTVzIGVhc2UtaW4tb3V0LCBib3JkZXItY29sb3IgMC4xNXMgZWFzZS1pbi1vdXQsXG4gIGJveC1zaGFkb3cgMC4xNXMgZWFzZS1pbi1vdXQ7XG5cbiRwYWdpbmF0aW9uLWJvcmRlci1yYWRpdXMtc206ICRib3JkZXItcmFkaXVzLXNtO1xuJHBhZ2luYXRpb24tYm9yZGVyLXJhZGl1cy1sZzogJGJvcmRlci1yYWRpdXMtbGc7XG4vLyBzY3NzLWRvY3MtZW5kIHBhZ2luYXRpb24tdmFyaWFibGVzXG5cbi8vIFBsYWNlaG9sZGVyc1xuXG4vLyBzY3NzLWRvY3Mtc3RhcnQgcGxhY2Vob2xkZXJzXG4kcGxhY2Vob2xkZXItb3BhY2l0eS1tYXg6IDAuNTtcbiRwbGFjZWhvbGRlci1vcGFjaXR5LW1pbjogMC4yO1xuLy8gc2Nzcy1kb2NzLWVuZCBwbGFjZWhvbGRlcnNcblxuLy8gQ2FyZHNcbiRjYXJkLXNwYWNlci15OiAyNXB4OyAvLyBjaGFuZ2VcbiRjYXJkLXNwYWNlci14OiAyNXB4OyAvLyBjaGFuZ2VcbiRjYXJkLXRpdGxlLXNwYWNlci15OiAkc3BhY2VyIC8gMjtcbiRjYXJkLWJvcmRlci13aWR0aDogMHB4OyAvLyBjaGFuZ2VcbiRjYXJkLWJvcmRlci1yYWRpdXM6ICRib3JkZXItcmFkaXVzO1xuJGNhcmQtYm9yZGVyLWNvbG9yOiByZ2JhKCRibGFjaywgMC4xMjUpO1xuJGNhcmQtaW5uZXItYm9yZGVyLXJhZGl1czogY2FsYygjeyRjYXJkLWJvcmRlci1yYWRpdXN9IC0gI3skY2FyZC1ib3JkZXItd2lkdGh9KTtcbiRjYXJkLWNhcC1wYWRkaW5nLXk6IDI1cHg7XG4kY2FyZC1jYXAtcGFkZGluZy14OiAyNXB4O1xuJGNhcmQtY2FwLWJnOiB0cmFuc3BhcmVudDtcbiRjYXJkLWNhcC1jb2xvcjogbnVsbDtcbiRjYXJkLWhlaWdodDogbnVsbDtcbiRjYXJkLWNvbG9yOiBudWxsO1xuJGNhcmQtYmc6ICR3aGl0ZTtcblxuJGNhcmQtaW1nLW92ZXJsYXktcGFkZGluZzogMS4yNXJlbTtcblxuJGNhcmQtZ3JvdXAtbWFyZ2luOiAkZ3JpZC1ndXR0ZXItd2lkdGggLyAyO1xuXG4vLyBBY2NvcmRpb25cbiRhY2NvcmRpb24tcGFkZGluZy15OiAxcmVtO1xuJGFjY29yZGlvbi1wYWRkaW5nLXg6IDEuMjVyZW07XG4kYWNjb3JkaW9uLWNvbG9yOiAkYm9keS1jb2xvcjtcbiRhY2NvcmRpb24tYmc6IHRyYW5zcGFyZW50O1xuJGFjY29yZGlvbi1ib3JkZXItd2lkdGg6ICRib3JkZXItd2lkdGg7XG4kYWNjb3JkaW9uLWJvcmRlci1jb2xvcjogcmdiYSgkYmxhY2ssIDAuMTI1KTtcbiRhY2NvcmRpb24tYm9yZGVyLXJhZGl1czogJGJvcmRlci1yYWRpdXM7XG4kYWNjb3JkaW9uLWlubmVyLWJvcmRlci1yYWRpdXM6IHN1YnRyYWN0KFxuICAkYWNjb3JkaW9uLWJvcmRlci1yYWRpdXMsXG4gICRhY2NvcmRpb24tYm9yZGVyLXdpZHRoXG4pO1xuXG4kYWNjb3JkaW9uLWJvZHktcGFkZGluZy15OiAkYWNjb3JkaW9uLXBhZGRpbmcteTtcbiRhY2NvcmRpb24tYm9keS1wYWRkaW5nLXg6ICRhY2NvcmRpb24tcGFkZGluZy14O1xuXG4kYWNjb3JkaW9uLWJ1dHRvbi1wYWRkaW5nLXk6ICRhY2NvcmRpb24tcGFkZGluZy15O1xuJGFjY29yZGlvbi1idXR0b24tcGFkZGluZy14OiAkYWNjb3JkaW9uLXBhZGRpbmcteDtcbiRhY2NvcmRpb24tYnV0dG9uLWNvbG9yOiAkYWNjb3JkaW9uLWNvbG9yO1xuJGFjY29yZGlvbi1idXR0b24tYmc6ICRhY2NvcmRpb24tYmc7XG4kYWNjb3JkaW9uLXRyYW5zaXRpb246ICRidG4tdHJhbnNpdGlvbiwgYm9yZGVyLXJhZGl1cyAwLjE1cyBlYXNlO1xuJGFjY29yZGlvbi1idXR0b24tYWN0aXZlLWJnOiB0aW50LWNvbG9yKCRkYXJrLCA5MCUpO1xuJGFjY29yZGlvbi1idXR0b24tYWN0aXZlLWNvbG9yOiBzaGFkZS1jb2xvcigkYm9keS1jb2xvciwgMTAlKTtcblxuJGFjY29yZGlvbi1idXR0b24tZm9jdXMtYm9yZGVyLWNvbG9yOiAkaW5wdXQtZm9jdXMtYm9yZGVyLWNvbG9yO1xuJGFjY29yZGlvbi1idXR0b24tZm9jdXMtYm94LXNoYWRvdzogJGJ0bi1mb2N1cy1ib3gtc2hhZG93O1xuXG4kYWNjb3JkaW9uLWljb24td2lkdGg6IDEuMjVyZW07XG4kYWNjb3JkaW9uLWljb24tY29sb3I6ICRhY2NvcmRpb24tY29sb3I7XG4kYWNjb3JkaW9uLWljb24tYWN0aXZlLWNvbG9yOiAkYWNjb3JkaW9uLWJ1dHRvbi1hY3RpdmUtY29sb3I7XG4kYWNjb3JkaW9uLWljb24tdHJhbnNpdGlvbjogdHJhbnNmb3JtIDAuMnMgZWFzZS1pbi1vdXQ7XG4kYWNjb3JkaW9uLWljb24tdHJhbnNmb3JtOiByb3RhdGUoMTgwZGVnKTtcblxuJGFjY29yZGlvbi1idXR0b24taWNvbjogdXJsKFwiZGF0YTppbWFnZS9zdmcreG1sLDxzdmcgeG1sbnM9J2h0dHA6Ly93d3cudzMub3JnLzIwMDAvc3ZnJyB2aWV3Qm94PScwIDAgMTYgMTYnIGZpbGw9JyN7JGFjY29yZGlvbi1pY29uLWNvbG9yfSc+PHBhdGggZmlsbC1ydWxlPSdldmVub2RkJyBkPSdNMS42NDYgNC42NDZhLjUuNSAwIDAgMSAuNzA4IDBMOCAxMC4yOTNsNS42NDYtNS42NDdhLjUuNSAwIDAgMSAuNzA4LjcwOGwtNiA2YS41LjUgMCAwIDEtLjcwOCAwbC02LTZhLjUuNSAwIDAgMSAwLS43MDh6Jy8+PC9zdmc+XCIpO1xuJGFjY29yZGlvbi1idXR0b24tYWN0aXZlLWljb246IHVybChcImRhdGE6aW1hZ2Uvc3ZnK3htbCw8c3ZnIHhtbG5zPSdodHRwOi8vd3d3LnczLm9yZy8yMDAwL3N2Zycgdmlld0JveD0nMCAwIDE2IDE2JyBmaWxsPScjeyRhY2NvcmRpb24taWNvbi1hY3RpdmUtY29sb3J9Jz48cGF0aCBmaWxsLXJ1bGU9J2V2ZW5vZGQnIGQ9J00xLjY0NiA0LjY0NmEuNS41IDAgMCAxIC43MDggMEw4IDEwLjI5M2w1LjY0Ni01LjY0N2EuNS41IDAgMCAxIC43MDguNzA4bC02IDZhLjUuNSAwIDAgMS0uNzA4IDBsLTYtNmEuNS41IDAgMCAxIDAtLjcwOHonLz48L3N2Zz5cIik7XG5cbi8vIFRvb2x0aXBzXG5cbiR0b29sdGlwLWZvbnQtc2l6ZTogJGZvbnQtc2l6ZS1zbTtcbiR0b29sdGlwLW1heC13aWR0aDogMjAwcHg7XG4kdG9vbHRpcC1jb2xvcjogJHdoaXRlO1xuJHRvb2x0aXAtYmc6ICRibGFjaztcbiR0b29sdGlwLWJvcmRlci1yYWRpdXM6ICRib3JkZXItcmFkaXVzO1xuJHRvb2x0aXAtb3BhY2l0eTogMC45O1xuJHRvb2x0aXAtcGFkZGluZy15OiAwLjI1cmVtO1xuJHRvb2x0aXAtcGFkZGluZy14OiAwLjVyZW07XG4kdG9vbHRpcC1tYXJnaW46IDA7XG5cbiR0b29sdGlwLWFycm93LXdpZHRoOiAwLjhyZW07XG4kdG9vbHRpcC1hcnJvdy1oZWlnaHQ6IDAuNHJlbTtcbiR0b29sdGlwLWFycm93LWNvbG9yOiAkdG9vbHRpcC1iZztcblxuLy8gRm9ybSB0b29sdGlwcyBtdXN0IGNvbWUgYWZ0ZXIgcmVndWxhciB0b29sdGlwc1xuJGZvcm0tZmVlZGJhY2stdG9vbHRpcC1wYWRkaW5nLXk6ICR0b29sdGlwLXBhZGRpbmcteTtcbiRmb3JtLWZlZWRiYWNrLXRvb2x0aXAtcGFkZGluZy14OiAkdG9vbHRpcC1wYWRkaW5nLXg7XG4kZm9ybS1mZWVkYmFjay10b29sdGlwLWZvbnQtc2l6ZTogJHRvb2x0aXAtZm9udC1zaXplO1xuJGZvcm0tZmVlZGJhY2stdG9vbHRpcC1saW5lLWhlaWdodDogJGxpbmUtaGVpZ2h0LWJhc2U7XG4kZm9ybS1mZWVkYmFjay10b29sdGlwLW9wYWNpdHk6ICR0b29sdGlwLW9wYWNpdHk7XG4kZm9ybS1mZWVkYmFjay10b29sdGlwLWJvcmRlci1yYWRpdXM6ICR0b29sdGlwLWJvcmRlci1yYWRpdXM7XG5cbi8vIFBvcG92ZXJzXG5cbiRwb3BvdmVyLWZvbnQtc2l6ZTogJGZvbnQtc2l6ZS1zbTtcbiRwb3BvdmVyLWJnOiAkd2hpdGU7XG4kcG9wb3Zlci1tYXgtd2lkdGg6IDI3NnB4O1xuJHBvcG92ZXItYm9yZGVyLXdpZHRoOiAkYm9yZGVyLXdpZHRoO1xuJHBvcG92ZXItYm9yZGVyLWNvbG9yOiByZ2JhKCRibGFjaywgMC4yKTtcbiRwb3BvdmVyLWJvcmRlci1yYWRpdXM6ICRib3JkZXItcmFkaXVzLWxnO1xuJHBvcG92ZXItaW5uZXItYm9yZGVyLXJhZGl1czogc3VidHJhY3QoXG4gICRwb3BvdmVyLWJvcmRlci1yYWRpdXMsXG4gICRwb3BvdmVyLWJvcmRlci13aWR0aFxuKTtcbiRwb3BvdmVyLWJveC1zaGFkb3c6ICRib3gtc2hhZG93O1xuXG4kcG9wb3Zlci1oZWFkZXItYmc6IGRhcmtlbigkcG9wb3Zlci1iZywgMyUpO1xuJHBvcG92ZXItaGVhZGVyLWNvbG9yOiAkaGVhZGluZ3MtY29sb3I7XG4kcG9wb3Zlci1oZWFkZXItcGFkZGluZy15OiAwLjVyZW07XG4kcG9wb3Zlci1oZWFkZXItcGFkZGluZy14OiAwLjc1cmVtO1xuXG4kcG9wb3Zlci1ib2R5LWNvbG9yOiAkYm9keS1jb2xvcjtcbiRwb3BvdmVyLWJvZHktcGFkZGluZy15OiAkc3BhY2VyO1xuJHBvcG92ZXItYm9keS1wYWRkaW5nLXg6ICRzcGFjZXI7XG5cbiRwb3BvdmVyLWFycm93LXdpZHRoOiAxcmVtO1xuJHBvcG92ZXItYXJyb3ctaGVpZ2h0OiAwLjVyZW07XG4kcG9wb3Zlci1hcnJvdy1jb2xvcjogJHBvcG92ZXItYmc7XG5cbiRwb3BvdmVyLWFycm93LW91dGVyLWNvbG9yOiBmYWRlLWluKCRwb3BvdmVyLWJvcmRlci1jb2xvciwgMC4wNSk7XG5cbi8vIFRvYXN0c1xuXG4kdG9hc3QtbWF4LXdpZHRoOiAzNTBweDtcbiR0b2FzdC1wYWRkaW5nLXg6IDAuNzVyZW07XG4kdG9hc3QtcGFkZGluZy15OiAwLjI1cmVtO1xuJHRvYXN0LWZvbnQtc2l6ZTogMC44NzVyZW07XG4kdG9hc3QtY29sb3I6IG51bGw7XG4kdG9hc3QtYmFja2dyb3VuZC1jb2xvcjogcmdiYSgkd2hpdGUsIDAuODUpO1xuJHRvYXN0LWJvcmRlci13aWR0aDogMXB4O1xuJHRvYXN0LWJvcmRlci1jb2xvcjogcmdiYSgwLCAwLCAwLCAwLjEpO1xuJHRvYXN0LWJvcmRlci1yYWRpdXM6IDAuMjVyZW07XG4kdG9hc3QtYm94LXNoYWRvdzogMCAwLjI1cmVtIDAuNzVyZW0gcmdiYSgkYmxhY2ssIDAuMSk7XG4kdG9hc3Qtc3BhY2luZzogJGNvbnRhaW5lci1wYWRkaW5nLXg7XG5cbiR0b2FzdC1oZWFkZXItY29sb3I6ICRncmF5LTYwMDtcbiR0b2FzdC1oZWFkZXItYmFja2dyb3VuZC1jb2xvcjogcmdiYSgkd2hpdGUsIDAuODUpO1xuJHRvYXN0LWhlYWRlci1ib3JkZXItY29sb3I6IHJnYmEoMCwgMCwgMCwgMC4wNSk7XG5cbi8vIEJhZGdlc1xuJGJhZGdlLWZvbnQtc2l6ZTogMC43NWVtO1xuJGJhZGdlLWZvbnQtd2VpZ2h0OiA1MDA7XG4kYmFkZ2UtY29sb3I6ICR3aGl0ZTtcbiRiYWRnZS1wYWRkaW5nLXk6IDAuMzVlbTtcbiRiYWRnZS1wYWRkaW5nLXg6IDAuNWVtO1xuJGJhZGdlLWJvcmRlci1yYWRpdXM6IDJweDtcblxuLy8gTW9kYWxzXG5cbi8vIFBhZGRpbmcgYXBwbGllZCB0byB0aGUgbW9kYWwgYm9keVxuJG1vZGFsLWlubmVyLXBhZGRpbmc6IDEuMjVyZW07IC8vIGNoYW5nZVxuXG4vLyBNYXJnaW4gYmV0d2VlbiBlbGVtZW50cyBpbiBmb290ZXIsIG11c3QgYmUgbG93ZXIgdGhhbiBvciBlcXVhbCB0byAyICogJG1vZGFsLWlubmVyLXBhZGRpbmdcbiRtb2RhbC1mb290ZXItbWFyZ2luLWJldHdlZW46IDAuNXJlbTtcbiRtb2RhbC1kaWFsb2ctbWFyZ2luOiAwLjVyZW07XG4kbW9kYWwtZGlhbG9nLW1hcmdpbi15LXNtLXVwOiAxLjc1cmVtO1xuXG4kbW9kYWwtdGl0bGUtbGluZS1oZWlnaHQ6ICRsaW5lLWhlaWdodC1iYXNlO1xuXG4kbW9kYWwtY29udGVudC1jb2xvcjogbnVsbDtcbiRtb2RhbC1jb250ZW50LWJnOiAkd2hpdGU7XG4kbW9kYWwtY29udGVudC1ib3JkZXItY29sb3I6IHJnYmEoJGJsYWNrLCAwLjIpO1xuJG1vZGFsLWNvbnRlbnQtYm9yZGVyLXdpZHRoOiAkYm9yZGVyLXdpZHRoO1xuJG1vZGFsLWNvbnRlbnQtYm9yZGVyLXJhZGl1czogJGJvcmRlci1yYWRpdXMtbGc7XG4kbW9kYWwtY29udGVudC1pbm5lci1ib3JkZXItcmFkaXVzOiBzdWJ0cmFjdChcbiAgJG1vZGFsLWNvbnRlbnQtYm9yZGVyLXJhZGl1cyxcbiAgJG1vZGFsLWNvbnRlbnQtYm9yZGVyLXdpZHRoXG4pO1xuJG1vZGFsLWNvbnRlbnQtYm94LXNoYWRvdy14czogMCAwLjI1cmVtIDAuNXJlbSByZ2JhKCRibGFjaywgMC41KTtcbiRtb2RhbC1jb250ZW50LWJveC1zaGFkb3ctc20tdXA6IDAgMC41cmVtIDFyZW0gcmdiYSgkYmxhY2ssIDAuNSk7XG5cbiRtb2RhbC1iYWNrZHJvcC1iZzogJGJsYWNrO1xuJG1vZGFsLWJhY2tkcm9wLW9wYWNpdHk6IDAuNTtcbiRtb2RhbC1oZWFkZXItYm9yZGVyLWNvbG9yOiAkYm9yZGVyLWNvbG9yO1xuJG1vZGFsLWZvb3Rlci1ib3JkZXItY29sb3I6ICRtb2RhbC1oZWFkZXItYm9yZGVyLWNvbG9yO1xuJG1vZGFsLWhlYWRlci1ib3JkZXItd2lkdGg6ICRtb2RhbC1jb250ZW50LWJvcmRlci13aWR0aDtcbiRtb2RhbC1mb290ZXItYm9yZGVyLXdpZHRoOiAkbW9kYWwtaGVhZGVyLWJvcmRlci13aWR0aDtcbiRtb2RhbC1oZWFkZXItcGFkZGluZy15OiAxLjI1cmVtOyAvLyBjaGFuZ2VcbiRtb2RhbC1oZWFkZXItcGFkZGluZy14OiAxLjU2MjVyZW07IC8vIGNoYW5nZVxuJG1vZGFsLWhlYWRlci1wYWRkaW5nOiAkbW9kYWwtaGVhZGVyLXBhZGRpbmcteSAkbW9kYWwtaGVhZGVyLXBhZGRpbmcteDsgLy8gY2hhbmdlXG5cbiRtb2RhbC1zbTogMzAwcHg7XG4kbW9kYWwtbWQ6IDUwMHB4O1xuJG1vZGFsLWxnOiA4MDBweDtcbiRtb2RhbC14bDogMTE0MHB4O1xuXG4kbW9kYWwtZmFkZS10cmFuc2Zvcm06IHRyYW5zbGF0ZSgwLCAtNTBweCk7XG4kbW9kYWwtc2hvdy10cmFuc2Zvcm06IG5vbmU7XG4kbW9kYWwtdHJhbnNpdGlvbjogdHJhbnNmb3JtIDAuM3MgZWFzZS1vdXQ7XG4kbW9kYWwtc2NhbGUtdHJhbnNmb3JtOiBzY2FsZSgxLjAyKTtcblxuLy8gQWxlcnRzXG4vL1xuLy8gRGVmaW5lIGFsZXJ0IGNvbG9ycywgYm9yZGVyIHJhZGl1cywgYW5kIHBhZGRpbmcuXG5cbiRhbGVydC1wYWRkaW5nLXk6IDAuNzVyZW07XG4kYWxlcnQtcGFkZGluZy14OiAxLjI1cmVtO1xuJGFsZXJ0LW1hcmdpbi1ib3R0b206IDFyZW07XG4kYWxlcnQtYm9yZGVyLXJhZGl1czogJGJvcmRlci1yYWRpdXM7XG4kYWxlcnQtbGluay1mb250LXdlaWdodDogJGZvbnQtd2VpZ2h0LWJvbGQ7XG4kYWxlcnQtYm9yZGVyLXdpZHRoOiAkYm9yZGVyLXdpZHRoO1xuXG4kYWxlcnQtYmctc2NhbGU6IC04MCU7XG4kYWxlcnQtYm9yZGVyLXNjYWxlOiAtNzAlO1xuJGFsZXJ0LWNvbG9yLXNjYWxlOiA0MCU7XG5cbiRhbGVydC1kaXNtaXNzaWJsZS1wYWRkaW5nLXI6ICRhbGVydC1wYWRkaW5nLXggKiAzOyAvLyAzeCBjb3ZlcnMgd2lkdGggb2YgeCBwbHVzIGRlZmF1bHQgcGFkZGluZyBvbiBlaXRoZXIgc2lkZVxuXG4vLyBQcm9ncmVzcyBiYXJzXG5cbiRwcm9ncmVzcy1oZWlnaHQ6IDFyZW07XG4kcHJvZ3Jlc3MtZm9udC1zaXplOiAkZm9udC1zaXplLWJhc2UgKiAwLjc1O1xuJHByb2dyZXNzLWJnOiAkYm9keS1iZzsgLy8gY2hhbmdlXG4kcHJvZ3Jlc3MtYm9yZGVyLXJhZGl1czogJGJvcmRlci1yYWRpdXM7XG4kcHJvZ3Jlc3MtYm94LXNoYWRvdzogaW5zZXQgMCAwLjFyZW0gMC4xcmVtIHJnYmEoJGJsYWNrLCAwLjEpO1xuJHByb2dyZXNzLWJhci1jb2xvcjogJHdoaXRlO1xuJHByb2dyZXNzLWJhci1iZzogJHByaW1hcnk7IC8vIGNoYW5nZVxuJHByb2dyZXNzLWJhci1hbmltYXRpb24tdGltaW5nOiAxcyBsaW5lYXIgaW5maW5pdGU7XG4kcHJvZ3Jlc3MtYmFyLXRyYW5zaXRpb246IHdpZHRoIDAuNnMgZWFzZTtcblxuLy8gTGlzdCBncm91cFxuXG4kbGlzdC1ncm91cC1jb2xvcjogbnVsbDtcbiRsaXN0LWdyb3VwLWJnOiAkd2hpdGU7XG4kbGlzdC1ncm91cC1ib3JkZXItY29sb3I6ICRib3JkZXItY29sb3I7XG4kbGlzdC1ncm91cC1ib3JkZXItd2lkdGg6ICRib3JkZXItd2lkdGg7XG4kbGlzdC1ncm91cC1ib3JkZXItcmFkaXVzOiAkYm9yZGVyLXJhZGl1cztcblxuJGxpc3QtZ3JvdXAtaXRlbS1wYWRkaW5nLXk6ICRjYXJkLXNwYWNlci15IC8gMS41O1xuJGxpc3QtZ3JvdXAtaXRlbS1wYWRkaW5nLXg6ICRjYXJkLXNwYWNlci14O1xuJGxpc3QtZ3JvdXAtaXRlbS1iZy1zY2FsZTogLTgwJTtcbiRsaXN0LWdyb3VwLWl0ZW0tY29sb3Itc2NhbGU6IDQwJTtcblxuJGxpc3QtZ3JvdXAtaG92ZXItYmc6ICRncmF5LTEwMDtcbiRsaXN0LWdyb3VwLWFjdGl2ZS1jb2xvcjogJGNvbXBvbmVudC1hY3RpdmUtY29sb3I7XG4kbGlzdC1ncm91cC1hY3RpdmUtYmc6ICRjb21wb25lbnQtYWN0aXZlLWJnO1xuJGxpc3QtZ3JvdXAtYWN0aXZlLWJvcmRlci1jb2xvcjogJGxpc3QtZ3JvdXAtYWN0aXZlLWJnO1xuXG4kbGlzdC1ncm91cC1kaXNhYmxlZC1jb2xvcjogJGdyYXktNjAwO1xuJGxpc3QtZ3JvdXAtZGlzYWJsZWQtYmc6ICRsaXN0LWdyb3VwLWJnO1xuXG4kbGlzdC1ncm91cC1hY3Rpb24tY29sb3I6ICRncmF5LTcwMDtcbiRsaXN0LWdyb3VwLWFjdGlvbi1ob3Zlci1jb2xvcjogJGxpc3QtZ3JvdXAtYWN0aW9uLWNvbG9yO1xuXG4kbGlzdC1ncm91cC1hY3Rpb24tYWN0aXZlLWNvbG9yOiAkYm9keS1jb2xvcjtcbiRsaXN0LWdyb3VwLWFjdGlvbi1hY3RpdmUtYmc6ICRncmF5LTIwMDtcblxuLy8gSW1hZ2UgdGh1bWJuYWlsc1xuXG4kdGh1bWJuYWlsLXBhZGRpbmc6IDAuMjVyZW07XG4kdGh1bWJuYWlsLWJnOiAkYm9keS1iZztcbiR0aHVtYm5haWwtYm9yZGVyLXdpZHRoOiAkYm9yZGVyLXdpZHRoO1xuJHRodW1ibmFpbC1ib3JkZXItY29sb3I6ICRib3JkZXItY29sb3I7XG4kdGh1bWJuYWlsLWJvcmRlci1yYWRpdXM6ICRib3JkZXItcmFkaXVzO1xuJHRodW1ibmFpbC1ib3gtc2hhZG93OiAwIDFweCAycHggcmdiYSgkYmxhY2ssIDAuMDc1KTtcblxuLy8gRmlndXJlc1xuXG4kZmlndXJlLWNhcHRpb24tZm9udC1zaXplOiA5MCU7XG4kZmlndXJlLWNhcHRpb24tY29sb3I6ICRncmF5LTYwMDtcblxuLy8gQnJlYWRjcnVtYnNcblxuJGJyZWFkY3J1bWItZm9udC1zaXplOiBudWxsO1xuJGJyZWFkY3J1bWItcGFkZGluZy15OiAwO1xuJGJyZWFkY3J1bWItcGFkZGluZy14OiAwO1xuJGJyZWFkY3J1bWItaXRlbS1wYWRkaW5nOiAwLjVyZW07XG5cbiRicmVhZGNydW1iLW1hcmdpbi1ib3R0b206IDFyZW07XG5cbiRicmVhZGNydW1iLWJnOiBudWxsO1xuJGJyZWFkY3J1bWItZGl2aWRlci1jb2xvcjogJGdyYXktNjAwO1xuJGJyZWFkY3J1bWItYWN0aXZlLWNvbG9yOiAkZ3JheS02MDA7XG4kYnJlYWRjcnVtYi1kaXZpZGVyOiB1cmwoXCJkYXRhOmltYWdlL3N2Zyt4bWwsJTNDc3ZnIHhtbG5zPSdodHRwOi8vd3d3LnczLm9yZy8yMDAwL3N2Zycgdmlld0JveD0nMCAwIDI0IDI0JyB3aWR0aD0nMTQnIGhlaWdodD0nMTQnIHN0cm9rZT0nI3skZ3JheS02MDB9JyBzdHJva2Utd2lkdGg9JzInIGZpbGw9J25vbmUnIHN0cm9rZS1saW5lY2FwPSdyb3VuZCcgc3Ryb2tlLWxpbmVqb2luPSdyb3VuZCcgY2xhc3M9J2Nzcy1pNmR6cTEnJTNFJTNDcG9seWxpbmUgcG9pbnRzPSc5IDE4IDE1IDEyIDkgNiclM0UlM0MvcG9seWxpbmUlM0UlM0Mvc3ZnJTNFXCIpO1xuJGJyZWFkY3J1bWItZGl2aWRlci1mbGlwcGVkOiAkYnJlYWRjcnVtYi1kaXZpZGVyO1xuJGJyZWFkY3J1bWItYm9yZGVyLXJhZGl1czogbnVsbDtcblxuLy8gQ2Fyb3VzZWxcblxuJGNhcm91c2VsLWNvbnRyb2wtY29sb3I6ICR3aGl0ZTtcbiRjYXJvdXNlbC1jb250cm9sLXdpZHRoOiAxNSU7XG4kY2Fyb3VzZWwtY29udHJvbC1vcGFjaXR5OiAwLjU7XG4kY2Fyb3VzZWwtY29udHJvbC1ob3Zlci1vcGFjaXR5OiAwLjk7XG4kY2Fyb3VzZWwtY29udHJvbC10cmFuc2l0aW9uOiBvcGFjaXR5IDAuMTVzIGVhc2U7XG5cbiRjYXJvdXNlbC1pbmRpY2F0b3Itd2lkdGg6IDMwcHg7XG4kY2Fyb3VzZWwtaW5kaWNhdG9yLWhlaWdodDogM3B4O1xuJGNhcm91c2VsLWluZGljYXRvci1oaXQtYXJlYS1oZWlnaHQ6IDEwcHg7XG4kY2Fyb3VzZWwtaW5kaWNhdG9yLXNwYWNlcjogM3B4O1xuJGNhcm91c2VsLWluZGljYXRvci1vcGFjaXR5OiAwLjU7XG4kY2Fyb3VzZWwtaW5kaWNhdG9yLWFjdGl2ZS1iZzogJHdoaXRlO1xuJGNhcm91c2VsLWluZGljYXRvci1hY3RpdmUtb3BhY2l0eTogMTtcbiRjYXJvdXNlbC1pbmRpY2F0b3ItdHJhbnNpdGlvbjogb3BhY2l0eSAwLjZzIGVhc2U7XG5cbiRjYXJvdXNlbC1jYXB0aW9uLXdpZHRoOiA3MCU7XG4kY2Fyb3VzZWwtY2FwdGlvbi1jb2xvcjogJHdoaXRlO1xuJGNhcm91c2VsLWNhcHRpb24tcGFkZGluZy15OiAxLjI1cmVtO1xuJGNhcm91c2VsLWNhcHRpb24tc3BhY2VyOiAxLjI1cmVtO1xuXG4kY2Fyb3VzZWwtY29udHJvbC1pY29uLXdpZHRoOiAyMHB4O1xuXG4kY2Fyb3VzZWwtY29udHJvbC1wcmV2LWljb24tYmc6IHVybChcImRhdGE6aW1hZ2Uvc3ZnK3htbCw8c3ZnIHhtbG5zPSdodHRwOi8vd3d3LnczLm9yZy8yMDAwL3N2ZycgZmlsbD0nI3skY2Fyb3VzZWwtY29udHJvbC1jb2xvcn0nIHZpZXdCb3g9JzAgMCA4IDgnPjxwYXRoIGQ9J001LjI1IDBsLTQgNCA0IDQgMS41LTEuNUw0LjI1IDRsMi41LTIuNUw1LjI1IDB6Jy8+PC9zdmc+XCIpO1xuJGNhcm91c2VsLWNvbnRyb2wtbmV4dC1pY29uLWJnOiB1cmwoXCJkYXRhOmltYWdlL3N2Zyt4bWwsPHN2ZyB4bWxucz0naHR0cDovL3d3dy53My5vcmcvMjAwMC9zdmcnIGZpbGw9JyN7JGNhcm91c2VsLWNvbnRyb2wtY29sb3J9JyB2aWV3Qm94PScwIDAgOCA4Jz48cGF0aCBkPSdNMi43NSAwbC0xLjUgMS41TDMuNzUgNGwtMi41IDIuNUwyLjc1IDhsNC00LTQtNHonLz48L3N2Zz5cIik7XG5cbiRjYXJvdXNlbC10cmFuc2l0aW9uLWR1cmF0aW9uOiAwLjZzO1xuJGNhcm91c2VsLXRyYW5zaXRpb246IHRyYW5zZm9ybSAkY2Fyb3VzZWwtdHJhbnNpdGlvbi1kdXJhdGlvbiBlYXNlLWluLW91dDsgLy8gRGVmaW5lIHRyYW5zZm9ybSB0cmFuc2l0aW9uIGZpcnN0IGlmIHVzaW5nIG11bHRpcGxlIHRyYW5zaXRpb25zIChlLmcuLCBgdHJhbnNmb3JtIDJzIGVhc2UsIG9wYWNpdHkgLjVzIGVhc2Utb3V0YClcblxuJGNhcm91c2VsLWRhcmstaW5kaWNhdG9yLWFjdGl2ZS1iZzogJGJsYWNrO1xuJGNhcm91c2VsLWRhcmstY2FwdGlvbi1jb2xvcjogJGJsYWNrO1xuJGNhcm91c2VsLWRhcmstY29udHJvbC1pY29uLWZpbHRlcjogaW52ZXJ0KDEpIGdyYXlzY2FsZSgxMDApO1xuXG4vLyBTcGlubmVyc1xuXG4kc3Bpbm5lci13aWR0aDogMnJlbTtcbiRzcGlubmVyLWhlaWdodDogJHNwaW5uZXItd2lkdGg7XG4kc3Bpbm5lci12ZXJ0aWNhbC1hbGlnbjogLTAuMTI1ZW07XG4kc3Bpbm5lci1ib3JkZXItd2lkdGg6IDAuMjVlbTtcbiRzcGlubmVyLWFuaW1hdGlvbi1zcGVlZDogMC43NXM7XG5cbiRzcGlubmVyLXdpZHRoLXNtOiAxcmVtO1xuJHNwaW5uZXItaGVpZ2h0LXNtOiAkc3Bpbm5lci13aWR0aC1zbTtcbiRzcGlubmVyLWJvcmRlci13aWR0aC1zbTogMC4yZW07XG5cbi8vIENsb3NlXG5cbiRidG4tY2xvc2Utd2lkdGg6IDFlbTtcbiRidG4tY2xvc2UtaGVpZ2h0OiAkYnRuLWNsb3NlLXdpZHRoO1xuJGJ0bi1jbG9zZS1wYWRkaW5nLXg6IDAuMjVlbTtcbiRidG4tY2xvc2UtcGFkZGluZy15OiAkYnRuLWNsb3NlLXBhZGRpbmcteDtcbiRidG4tY2xvc2UtY29sb3I6ICRibGFjaztcbiRidG4tY2xvc2UtYmc6IHVybChcImRhdGE6aW1hZ2Uvc3ZnK3htbCw8c3ZnIHhtbG5zPSdodHRwOi8vd3d3LnczLm9yZy8yMDAwL3N2ZycgZmlsbD0nI3skYnRuLWNsb3NlLWNvbG9yfScgdmlld0JveD0nMCAwIDE2IDE2Jz48cGF0aCBkPSdNLjI5My4yOTNhMSAxIDAgMDExLjQxNCAwTDggNi41ODYgMTQuMjkzLjI5M2ExIDEgMCAxMTEuNDE0IDEuNDE0TDkuNDE0IDhsNi4yOTMgNi4yOTNhMSAxIDAgMDEtMS40MTQgMS40MTRMOCA5LjQxNGwtNi4yOTMgNi4yOTNhMSAxIDAgMDEtMS40MTQtMS40MTRMNi41ODYgOCAuMjkzIDEuNzA3YTEgMSAwIDAxMC0xLjQxNHonLz48L3N2Zz5cIik7XG4kYnRuLWNsb3NlLWZvY3VzLXNoYWRvdzogJGlucHV0LWJ0bi1mb2N1cy1ib3gtc2hhZG93O1xuJGJ0bi1jbG9zZS1vcGFjaXR5OiAwLjU7XG4kYnRuLWNsb3NlLWhvdmVyLW9wYWNpdHk6IDAuNzU7XG4kYnRuLWNsb3NlLWZvY3VzLW9wYWNpdHk6IDE7XG4kYnRuLWNsb3NlLWRpc2FibGVkLW9wYWNpdHk6IDAuMjU7XG4kYnRuLWNsb3NlLXdoaXRlLWZpbHRlcjogaW52ZXJ0KDEpIGdyYXlzY2FsZSgxMDAlKSBicmlnaHRuZXNzKDIwMCUpO1xuXG4vLyBPZmZjYW52YXNcblxuLy8gc2Nzcy1kb2NzLXN0YXJ0IG9mZmNhbnZhcy12YXJpYWJsZXNcbiRvZmZjYW52YXMtcGFkZGluZy15OiAkbW9kYWwtaW5uZXItcGFkZGluZztcbiRvZmZjYW52YXMtcGFkZGluZy14OiAkbW9kYWwtaW5uZXItcGFkZGluZztcbiRvZmZjYW52YXMtaG9yaXpvbnRhbC13aWR0aDogMzYwcHg7XG4kb2ZmY2FudmFzLXZlcnRpY2FsLWhlaWdodDogMzN2aDtcbiRvZmZjYW52YXMtdHJhbnNpdGlvbi1kdXJhdGlvbjogMC4zcztcbiRvZmZjYW52YXMtYm9yZGVyLWNvbG9yOiAkbW9kYWwtY29udGVudC1ib3JkZXItY29sb3I7XG4kb2ZmY2FudmFzLWJvcmRlci13aWR0aDogJG1vZGFsLWNvbnRlbnQtYm9yZGVyLXdpZHRoO1xuJG9mZmNhbnZhcy10aXRsZS1saW5lLWhlaWdodDogJG1vZGFsLXRpdGxlLWxpbmUtaGVpZ2h0O1xuJG9mZmNhbnZhcy1iZy1jb2xvcjogJG1vZGFsLWNvbnRlbnQtYmc7XG4kb2ZmY2FudmFzLWNvbG9yOiAkbW9kYWwtY29udGVudC1jb2xvcjtcbiRvZmZjYW52YXMtYm94LXNoYWRvdzogJG1vZGFsLWNvbnRlbnQtYm94LXNoYWRvdy14cztcbi8vIHNjc3MtZG9jcy1lbmQgb2ZmY2FudmFzLXZhcmlhYmxlc1xuXG4vLyBDb2RlXG5cbiRjb2RlLWZvbnQtc2l6ZTogODcuNSU7XG4kY29kZS1jb2xvcjogJHBpbms7XG5cbiRrYmQtcGFkZGluZy15OiAwLjJyZW07XG4ka2JkLXBhZGRpbmcteDogMC40cmVtO1xuJGtiZC1mb250LXNpemU6ICRjb2RlLWZvbnQtc2l6ZTtcbiRrYmQtY29sb3I6ICR3aGl0ZTtcbiRrYmQtYmc6ICRncmF5LTkwMDtcblxuJHByZS1jb2xvcjogbnVsbDtcbiIsIi8vID09PT09PT09PT09PT09PT09PT09PT09PT09PT09PT09PT09PT09PVxuLy8gICAgIExpc3Qgb2YgdmFyaWFibGVzIGZvciBsYXlvdXRcbi8vID09PT09PT09PT09PT09PT09PT09PT09PT09PT09PT09PT09PT09PVxuXG4kaGVhZGVyLWhlaWdodDogNzBweDtcbiRzaWRlYmFyLXdpZHRoOiAyNTVweDtcbiRzaWRlYmFyLWNvbGxhcHNlZC13aWR0aDogNzBweDtcblxuLy8gaGVhZGVyXG4kaGVhZGVyLWJhY2tncm91bmQ6ICNmZmY7XG4kaGVhZGVyLWNvbG9yOiAjNTI1YjY5O1xuJGhlYWRlci1zaGFkb3c6IDAgMXB4IDIwcHggMCByZ2JhKDY5LCA5MCwgMTAwLCAwLjA4KTtcbiRicmFuZC1jb2xvcjogIzE2MWMyNTtcbiRoZWFkZXItaXRlbS1ib3JkZXItY29sb3I6ICNDRUNFQ0U7XG5cblxuLy8gTWVudVxuJHNpZGViYXItYmFja2dyb3VuZDogIzFjMjMyZjtcbiRzaWRlYmFyLWNvbG9yOiAjY2VkNGRjO1xuJHNpZGViYXItaWNvbi1jb2xvcjogIzc3ODI5MDtcbiRzaWRlYmFyLW1haW4tYWN0aXZlLWNvbG9yOiAjZmZmO1xuJHNpZGViYXItYWN0aXZlLWNvbG9yOiAkcHJpbWFyeTtcbiRzaWRlYmFyLWNhcHRpb246ICRoZWFkZXItY29sb3I7XG4kc2lkZWJhci1zaGFkb3c6IDAgMXB4IDIwcHggMCByZ2JhKDY5LCA5MCwgMTAwLCAwLjA4KTtcblxuLy8gaG9yaXpvbnRhbCBtZW51XG4kdG9wYmFyLWhlaWdodDogNjBweDtcbiR0b3BiYXItYmFja2dyb3VuZDogIzFjMjMyZjtcbiR0b3BiYXItY29sb3I6ICNiNWJkY2E7XG4kaGVhZGVyLXN1Ym1lbnUtYmFja2dyb3VuZDogI2ZmZjtcbiRoZWFkZXItc3VibWVudS1jb2xvcjogIzFjMjMyZjtcblxuLy8gY2FyZCBibG9ja1xuJGNhcmQtc2hhZG93OiAwIDZweCAzMHB4IHJnYmEoMTgyLCAxODYsIDIwMywgMC4zKTtcblxuJHNvZnQtYmctbGV2ZWw6IC04MCU7XG5cbiR0aGVtZXMtY29sb3JzOiAoXG4gICAvLyB7IEZvciBHcmFkaWVudCBlZmZlY3R9XG4gIC8vIHRoZW1lLTE6IChcbiAgLy8gICBib2R5OiBsaW5lYXItZ3JhZGllbnQoMTE1LjI1ZGVnLCAjZmZmZmZmIDAlLCAjZjhmOWZkIDEwMC4yNSUpLFxuICAvLyAgIHByaW1hcnk6ICM1MTQ1OWQsXG4gIC8vICAgcHJpbWFyeS1ncmQ6XG4gIC8vICAgICBsaW5lYXItZ3JhZGllbnQoMTQxLjU1ZGVnLHJnYmEoODEsIDY5LCAxNTcsIDApIDMuNDYlLHJnYmEoMjU1LCA1OCwgMTEwLCAwLjYpIDk5Ljg2JSksXG4gIC8vICksXG4vLyB7IEZvciBQbGFpbiBlZmZlY3R9XG4gIC8vIHRoZW1lLTE6IChcbiAgLy8gICBib2R5OlxuICAvLyAgICAgbGluZWFyLWdyYWRpZW50KDE0MS41NWRlZywgcmdiYSgyNDAsIDI0NCwgMjQzLCAwKSAzLjQ2JSwgI2YwZjRmMyA5OS44NiUpLFxuICAvLyAgIHByaW1hcnk6ICM1ODRlZDIsXG4gIC8vICAgcHJpbWFyeS1ncmQ6XG4gIC8vICAgICBsaW5lYXItZ3JhZGllbnQoMTQxLjU1ZGVnLCByZ2JhKDEwNCwgOTQsIDIyOSwgMCkgMy40NiUsICM2ODVlZTUgOTkuODYlKSxcbiAgLy8gKSxcblxuICB0aGVtZS0xOiAoXG4gICAgYm9keTpcbiAgICAgIGxpbmVhci1ncmFkaWVudCgxNDEuNTVkZWcsIHJnYmEoMjQwLCAyNDQsIDI0MywgMCkgMy40NiUsICNmMGY0ZjMgOTkuODYlKSxcbiAgICBwcmltYXJ5OiAjMENBRjYwLFxuICAgIHByaW1hcnktZ3JkOlxuICAgICAgbGluZWFyLWdyYWRpZW50KDE0MS41NWRlZywgIzBDQUY2MCAzLjQ2JSwgIzBDQUY2MCA5OS44NiUpLFxuICApLFxuXG4gIHRoZW1lLTI6IChcbiAgICBib2R5OlxuICAgICAgbGluZWFyLWdyYWRpZW50KDE0MS41NWRlZywgcmdiYSgyNDAsIDI0NCwgMjQzLCAwKSAzLjQ2JSwgI2YwZjRmMyA5OS44NiUpLFxuICAgIHByaW1hcnk6ICM2RkQ5NDMsXG4gICAgcHJpbWFyeS1ncmQ6XG4gICAgICBsaW5lYXItZ3JhZGllbnQoMTQxLjU1ZGVnLCAjNkZEOTQzIDMuNDYlLCAjNkZEOTQzIDk5Ljg2JSksXG4gICksXG5cbiAgdGhlbWUtMzogKFxuICAgIGJvZHk6IGxpbmVhci1ncmFkaWVudCgxMTUuMjVkZWcsICNmZmZmZmYgMCUsICNmOGY5ZmQgMTAwLjI1JSksXG4gICAgcHJpbWFyeTogIzU4NEVEMixcbiAgICBwcmltYXJ5LWdyZDogbGluZWFyLWdyYWRpZW50KDE0MS41NWRlZywgIzU4NEVEMiAzLjQ2JSwgIzU4NEVEMiA5OS44NiUpLFxuICApLFxuXG4gIHRoZW1lLTQ6IChcbiAgICBib2R5OlxuICAgICAgbGluZWFyLWdyYWRpZW50KDE0MS41NWRlZywgcmdiYSgyNDAsIDI0NCwgMjQzLCAwKSAzLjQ2JSwgI2YwZjRmMyA5OS44NiUpLFxuICAgIHByaW1hcnk6ICMxNDUzODgsXG4gICAgcHJpbWFyeS1ncmQ6bGluZWFyLWdyYWRpZW50KDE0MS41NWRlZywgIzE0NTM4OCAzLjQ2JSwgIzE0NTM4OCA5OS44NiUpLFxuICApLFxuICB0aGVtZS01OiAoXG4gICAgYm9keTpcbiAgICAgIGxpbmVhci1ncmFkaWVudCgxNDEuNTVkZWcsIHJnYmEoMjQwLCAyNDQsIDI0MywgMCkgMy40NiUsICNmMGY0ZjMgOTkuODYlKSxcbiAgICBwcmltYXJ5OiAjQjk0MDZCLFxuICAgIHByaW1hcnktZ3JkOiBsaW5lYXItZ3JhZGllbnQoMTQxLjU1ZGVnLCAjQjk0MDZCIDMuNDYlLCAjQjk0MDZCIDk5Ljg2JSksXG4gICksXG4gIHRoZW1lLTY6IChcbiAgICBib2R5OiNGRUZFRkUsXG4gICAgcHJpbWFyeTogIzAwOEVDQyxcbiAgICBwcmltYXJ5LWdyZDpsaW5lYXItZ3JhZGllbnQoMTQxLjU1ZGVnLCAjMDA4RUNDIDMuNDYlLCAjMDA4RUNDIDk5Ljg2JSksXG4gICksXG4gIHRoZW1lLTc6IChcbiAgICBib2R5OlxuICAgICAgbGluZWFyLWdyYWRpZW50KDE0MS41NWRlZywgcmdiYSgyNDAsIDI0NCwgMjQzLCAwKSAzLjQ2JSwgI2YwZjRmMyA5OS44NiUpLFxuICAgIHByaW1hcnk6ICM5MjJDODgsXG4gICAgcHJpbWFyeS1ncmQ6bGluZWFyLWdyYWRpZW50KDE0MS41NWRlZywgIzkyMkM4OCAzLjQ2JSwgIzkyMkM4OCA5OS44NiUpLFxuICApLFxuICB0aGVtZS04OiAoXG4gICAgYm9keTpcbiAgICAgIGxpbmVhci1ncmFkaWVudCgxNDEuNTVkZWcsIHJnYmEoMjQwLCAyNDQsIDI0MywgMCkgMy40NiUsICNmMGY0ZjMgOTkuODYlKSxcbiAgICBwcmltYXJ5OiAjQzBBMTQ1LFxuICAgIHByaW1hcnktZ3JkOmxpbmVhci1ncmFkaWVudCgxNDEuNTVkZWcsICNDMEExNDUgMy40NiUsICNDMEExNDUgOTkuODYlKSxcbiAgKSxcbiAgdGhlbWUtOTogKFxuICAgIGJvZHk6XG4gICAgICBsaW5lYXItZ3JhZGllbnQoMTQxLjU1ZGVnLCByZ2JhKDI0MCwgMjQ0LCAyNDMsIDApIDMuNDYlLCAjZjBmNGYzIDk5Ljg2JSksXG4gICAgcHJpbWFyeTogIzQ4NDk0QixcbiAgICBwcmltYXJ5LWdyZDpsaW5lYXItZ3JhZGllbnQoMTQxLjU1ZGVnLCAjNDg0OTRCIDMuNDYlLCAjNDg0OTRCIDk5Ljg2JSksXG4gICksXG4gIHRoZW1lLTEwOiAoXG4gICAgYm9keTpcbiAgICAgIGxpbmVhci1ncmFkaWVudCgxNDEuNTVkZWcsIHJnYmEoMjQwLCAyNDQsIDI0MywgMCkgMy40NiUsICNmMGY0ZjMgOTkuODYlKSxcbiAgICBwcmltYXJ5OiAjMEM3Nzg1LFxuICAgIHByaW1hcnktZ3JkOmxpbmVhci1ncmFkaWVudCgxNDEuNTVkZWcsICMwQzc3ODUgMy40NiUsICMwQzc3ODUgOTkuODYlKSxcbiAgKSxcbik7XG4iXX0= */
